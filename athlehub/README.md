# Athlehub - Professional Sports Management Platform

Athlehub is a comprehensive mobile application for tracking sports matches with professional-grade features including real-time match tracking, detailed player statistics, comprehensive rule configurations, and multiple match formats. Now featuring Version 2.5 with enhanced match setup flow and professional rule systems!

## 🚀 Features

### Version 2.5 - Professional Match Setup & Rule Systems
- **🧭 6-Step Enhanced Match Setup Flow**: Comprehensive guided setup with sport-specific configurations
- **⚙️ Professional Rule Configurations**: 15+ official rule presets (NBA, FIBA, FIFA, FIVB, BWF, ITTF)
- **🏟️ Advanced Match Formats**: Single Game, Set-Based, Tournament, and Rotational Play (Winner Stays)
- **📏 Team Size Intelligence**: 1v1 through 11v11 with size-specific rule adaptations
- **🎯 Statistics Intensity Levels**: Basic, Intermediate, Advanced, and Professional tracking
- **📍 Location-Based System**: Smart court/field management with player suggestions
- **🏆 Competitive Mode**: Professional rule enforcement with comprehensive validation
- **🔄 Tiebreaker Systems**: Multiple resolution methods for draws and overtime scenarios

### Version 2.0 - Enhanced Match Tracking & Player Stats
- **🔴 Real-Time Match Mode**: Live match tracking with timer, live scoring, and real-time stat updates
- **📝 Past Match Logging**: Form-style interface for entering completed matches
- **👥 Player Management**: Individual player tracking with auto-suggestions based on location history
- **📊 Detailed Statistics**: Sport-specific player stats (points, assists, rebounds, goals, saves, etc.)
- **⚙️ Match Configuration**: Customizable match types, team sizes, and scoring systems
- **🎯 Multi-Sport Support**: Enhanced configurations for Basketball, Football, Badminton, Table Tennis, Volleyball

### Version 1.0 - Core Features (Still Available)
- **User Authentication**: Sign up and login with email/password
- **Match Logging**: Record matches for various sports
- **Match History**: View all previously logged matches
- **User Profile**: Basic profile management

## Tech Stack

- **Frontend**: React Native with Expo
- **Backend**: Supabase (Authentication, Database)
- **State Management**: React Context API
- **UI Components**: React Native Elements

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Expo CLI
- Supabase account

### Installation

1. Install dependencies:

   ```bash
   npm install
   ```

2. Set up Supabase:
   - Create a new Supabase project
   - Run the SQL from `supabase/schema.sql` in the Supabase SQL editor
   - Copy your Supabase URL and anon key

3. Configure Supabase:
   - The Supabase credentials are already configured in `lib/supabase.js`
   - If you want to use your own Supabase project, replace the URL and anon key

### Running the App

```bash
npm start
```

Then, scan the QR code with the Expo Go app on your mobile device or use an emulator.

## Project Structure

```
athlehub/
├── App.js                  # Main application entry point
├── components/             # Reusable UI components
│   └── match/              # Match-related components
│       ├── SportConfig.js  # Sport configuration component
│       └── PlayerManager.js # Player management component
├── contexts/               # React Context providers
│   └── AuthContext.js      # Authentication context
├── lib/                    # Utility libraries
│   └── supabase.js         # Enhanced Supabase client with custom REST implementation
├── navigation/             # Navigation configuration
│   └── index.js            # Main navigation setup with V2 screens
├── screens/                # Application screens
│   ├── auth/               # Authentication screens
│   │   ├── SignInScreen.js
│   │   └── SignUpScreen.js
│   ├── match/              # Match creation and tracking screens
│   │   ├── SelectSportScreen.js      # Sport selection (V1)
│   │   ├── TeamSetupScreen.js        # Team setup (V1)
│   │   ├── ScoreInputScreen.js       # Score input (V1)
│   │   ├── EnhancedMatchSetupScreen.js # V2.5 6-step enhanced setup flow
│   │   ├── MatchRulesConfigScreen.js # V2.5 Professional rule configuration
│   │   ├── LiveMatchScreen.js        # V2 Real-time match tracking
│   │   └── PastMatchEntryScreen.js   # V2 Past match logging
│   ├── HomeScreen.js       # Home screen
│   ├── MatchHistoryScreen.js # Enhanced match history with player stats
│   └── ProfileScreen.js    # User profile screen
├── services/               # Business logic services
│   └── locationService.js  # Location-based features and court management
└── supabase/               # Supabase configuration
    ├── schema.sql          # V2 Enhanced database schema
    ├── v2_migration.sql    # Migration script for V1 to V2
    ├── enhanced_rules_migration.sql # V2.5 Professional rule systems
    └── performance_optimizations.sql # Performance improvements
```

## Current Status

🚀 **Version 2.5 - Professional Sports Management Platform** - Now with comprehensive match setup flow and professional rule systems!

### V2.5 Features Working:
- ✅ **6-Step Enhanced Match Setup Flow**: Sport selection → Match configuration → Team setup → Rule configuration → Statistics setup → Summary
- ✅ **Professional Rule Systems**: 15+ official rule presets with team size-specific adaptations
- ✅ **Advanced Match Formats**: Single Game, Set-Based, Tournament, and Rotational Play with tiebreaker systems
- ✅ **Comprehensive Rule Configuration**: Timing, scoring, fouls, substitutions, court/field specifications
- ✅ **Location-Based Intelligence**: Smart court database with player suggestions and popular location tracking
- ✅ **Statistics Intensity Levels**: 4-tier tracking system from basic to professional analytics
- ✅ **Enhanced Database Schema**: JSONB rule storage with validation and optimized queries

### V2.0 Features Working:
- ✅ Real-time match tracking with live timer and scoring
- ✅ Past match entry with detailed player statistics
- ✅ Player management with auto-suggestions
- ✅ Competitive mode with jersey number tracking
- ✅ Sport-specific stat tracking (Basketball, Football, Badminton, Table Tennis, Volleyball)

### V1 Features (Still Working):
- ✅ User authentication (sign up/login)
- ✅ Real-time database integration
- ✅ Match logging with persistent storage
- ✅ Match history with real data
- ✅ User profiles with database storage
- ✅ All navigation flows working

### Database Setup:
The app is currently connected to a pre-configured Supabase database with:
- **Enhanced V2.5 Schema**: JSONB rule storage, team size tracking, and professional rule configurations
- **Comprehensive Tables**: User profiles, players, locations, match participants, player stats, and enhanced matches
- **Sports Configuration**: Enhanced sports table with team size support, match durations, and rule presets
- **Match Rule Storage**: JSONB fields for comprehensive rule configurations with validation functions
- **Location Intelligence**: Court/field database with player suggestion capabilities
- **Performance Optimized**: Indexes, views, and optimized RLS policies for fast queries

### Database Migration:
To upgrade existing database to V2.5:
1. **V1 to V2**: Run `supabase/v2_migration.sql` for basic V2 features
2. **V2 to V2.5**: Run `supabase/enhanced_rules_migration.sql` for professional rule systems
3. **Performance**: Run `supabase/performance_optimizations.sql` for query optimization

### Database Features:
- **Rule Validation**: JSONB validation functions ensure rule configuration integrity
- **Enhanced Views**: `enhanced_matches` view with extracted rule fields for easy querying
- **Smart Indexing**: Optimized indexes for rule-based searches and location queries
- **Backward Compatibility**: All migrations preserve existing data

## Version 2.0 Architecture

### Match Flow Options:
1. **Enhanced Setup Flow**: Sport selection → Configuration → Match mode → Player management → Review
2. **Real-Time Mode**: Live match with timer, live scoring, and real-time stat tracking
3. **Past Entry Mode**: Form-based entry for completed matches with player stats

### Player Management:
- Individual player tracking across matches
- Location-based player suggestions
- Jersey number management in competitive mode
- Automatic match count updates

### Statistics Tracking:
- **Basketball**: Points, Assists, Rebounds, Steals, Blocks, Field Goals, Free Throws
- **Football**: Goals, Assists, Saves, Cards
- **Racket Sports**: Points, Aces, Faults, Winners
- **Volleyball**: Points, Spikes, Digs, Serve Aces

## 📚 Documentation

### **Comprehensive Guides**
- **[Enhanced Match Setup Flow](docs/MATCH_SETUP_FLOW.md)** - Complete 6-step setup process documentation
- **[Match Formats Guide](docs/MATCH_FORMATS.md)** - Single Game, Set-Based, Tournament, and Rotational Play formats
- **[Professional Rule Systems](docs/PROFESSIONAL_RULE_SYSTEMS.md)** - 15+ official rule presets and customization options
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Database schema, endpoints, and integration examples
- **[Enhanced Rules Implementation](ENHANCED_RULES_IMPLEMENTATION.md)** - Technical implementation details

### **Quick Reference**
- **Match Setup**: 6-step guided process with sport-specific configurations
- **Rule Presets**: NBA, FIBA, NCAA, FIFA, FIVB, BWF, ITTF official rules
- **Team Sizes**: 1v1 through 11v11 with size-specific adaptations
- **Statistics**: 4-tier tracking system (Basic → Professional)
- **Locations**: Smart court/field management with player suggestions

## Next Steps (V3 Roadmap)

- **Advanced Analytics**: Player efficiency ratings, performance trends, shot heatmaps
- **Tournament Management**: Full bracket system with group stages and knockouts
- **Social Features**: Friends, team formation, match sharing, comments
- **Gamification**: Achievements, leaderboards, player rankings
- **Enhanced Real-Time**: Spectator mode, live streaming integration
- **Mobile Optimizations**: Offline mode, push notifications, background sync
