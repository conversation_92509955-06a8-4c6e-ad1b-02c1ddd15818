import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import SportConfig from '../../components/match/SportConfig';
import PlayerManager from '../../components/match/PlayerManager';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';

const EnhancedMatchSetupScreen = ({ navigation, route }) => {
  const { sport: initialSport, initialConfig, startAtStep, teamAName: initialTeamAName, teamBName: initialTeamBName } = route?.params || {};
  const { user } = useAuth();

  const [sport, setSport] = useState(initialSport || null);
  const [currentStep, setCurrentStep] = useState(startAtStep || 0);
  const [matchConfig, setMatchConfig] = useState(initialConfig || {
    matchType: 'single',
    teamSize: '5v5',
    scoringSystem: 'standard',
    competitiveMode: false,
    matchMode: 'real_time', // or 'past_entry'
  });

  const [teamAName, setTeamAName] = useState(initialTeamAName || 'Team A');
  const [teamBName, setTeamBName] = useState(initialTeamBName || 'Team B');
  const [teamAPlayers, setTeamAPlayers] = useState([]);
  const [teamBPlayers, setTeamBPlayers] = useState([]);
  const [suggestedPlayers, setSuggestedPlayers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sports, setSports] = useState([]);
  const [sportsLoading, setSportsLoading] = useState(false);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const steps = [
    { title: 'Sport + Match Type', component: 'sport_and_type' },
    { title: 'Match Setup', component: 'match_setup' },
    { title: 'Team Setup', component: 'team_setup' },
    { title: 'Match Rules', component: 'match_rules' },
    { title: 'Stat Intensity', component: 'stat_intensity' },
    { title: 'Summary', component: 'summary' },
  ];

  useEffect(() => {
    loadSuggestedPlayers();
    if (!sport) {
      loadSports();
    }
  }, []);

  const loadSports = async () => {
    try {
      setSportsLoading(true);
      const { data, error } = await supabase
        .from('sports')
        .select('*')
        .order('name');

      if (error) throw error;
      setSports(data || []);
    } catch (error) {
      console.error('Error fetching sports:', error.message);
      // Fallback to default sports if database query fails
      const fallbackSports = [
        { id: 1, name: 'Basketball', icon: 'basketball' },
        { id: 2, name: 'Football', icon: 'football' },
        { id: 3, name: 'Badminton', icon: 'badminton' },
        { id: 4, name: 'Table Tennis', icon: 'table-tennis' },
        { id: 5, name: 'Volleyball', icon: 'volleyball' }
      ];
      setSports(fallbackSports);
    } finally {
      setSportsLoading(false);
    }
  };

  const loadSuggestedPlayers = async () => {
    try {
      // Load players that have been used before
      // Note: Our custom client doesn't support complex queries yet
      // For now, we'll just set empty array - this will be enhanced in future updates
      setSuggestedPlayers([]);
    } catch (error) {
      console.error('Error loading suggested players:', error);
    }
  };

  const getMaxPlayersPerTeam = () => {
    const teamSizeConfig = matchConfig.teamSize;
    if (teamSizeConfig.includes('v')) {
      return parseInt(teamSizeConfig.split('v')[0]);
    }
    return 5; // default
  };

  const getSportIcon = (sportName) => {
    switch (sportName?.toLowerCase()) {
      case 'basketball':
        return 'basketball-outline';
      case 'football':
        return 'football-outline';
      case 'badminton':
        return 'tennisball-outline';
      case 'table tennis':
        return 'tennisball-outline';
      case 'volleyball':
        return 'baseball-outline';
      default:
        return 'trophy-outline';
    }
  };

  const getAvailableMatchModes = () => {
    const allModes = [
      { id: 'single', name: 'Single Game', description: 'A standard match with a final score' },
      { id: 'set_based', name: 'Set-Based Match', description: 'Played over multiple sets (best of 3/5)' },
      { id: 'tournament', name: 'Tournament', description: 'Part of a bracket/league' },
      { id: 'rotational', name: 'Rotational Play', description: 'Multiple teams rotate in; winner stays' }
    ];

    // Filter based on sport
    if (!sport) return allModes;

    switch (sport.name.toLowerCase()) {
      case 'volleyball':
      case 'table tennis':
      case 'badminton':
        return allModes; // All modes supported
      case 'basketball':
      case 'football':
        return allModes.filter(mode => mode.id !== 'set_based'); // No set-based for these sports
      default:
        return allModes;
    }
  };

  const getAvailableTeamSizes = () => {
    if (!sport) return ['1v1', '2v2', '3v3', '5v5'];

    switch (sport.name.toLowerCase()) {
      case 'basketball':
        return ['1v1', '2v2', '3v3', '4v4', '5v5'];
      case 'football':
        return ['3v3', '4v4', '5v5', '7v7', '11v11'];
      case 'volleyball':
        return ['2v2', '3v3', '4v4', '6v6'];
      case 'table tennis':
      case 'badminton':
        return ['1v1', '2v2'];
      default:
        return ['1v1', '2v2', '3v3', '5v5'];
    }
  };

  const validateCurrentStep = () => {
    switch (currentStep) {
      case 0: // Sport + Match Type
        return sport !== null && matchConfig.matchMode !== null;
      case 1: // Match Setup (Mode + Team Size + Location + Date)
        return matchConfig.matchType && matchConfig.teamSize && matchConfig.location;
      case 2: // Team Setup + Players
        const minPlayers = 1;
        return teamAPlayers.length >= minPlayers && teamBPlayers.length >= minPlayers;
      case 3: // Match Rules
        return matchConfig.rules && matchConfig.rules.statTrackingIntensity;
      case 4: // Stat Intensity
        return matchConfig.rules && matchConfig.rules.statTrackingIntensity;
      case 5: // Summary
        return true;
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (!validateCurrentStep()) {
      Alert.alert('Incomplete', 'Please complete all required fields before continuing.');
      return;
    }

    // After Team Setup step, navigate to Rules Configuration
    if (currentStep === 2) {
      navigation.navigate('MatchRulesConfig', {
        sport,
        matchConfig,
        teamAName,
        teamBName,
        returnTo: 'EnhancedMatchSetup',
        nextStep: 3, // Continue to Match Rules step
      });
      return;
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      startMatch();
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      navigation.goBack();
    }
  };

  const startMatch = async () => {
    try {
      setLoading(true);

      // Enhanced match data with comprehensive rule configuration
      const matchData = {
        user_id: user.id,
        sport_id: sport.id,
        team_a_name: teamAName,
        team_b_name: teamBName,
        team_a_score: 0,
        team_b_score: 0,
        match_date: new Date().toISOString(),

        // Enhanced V2 fields
        location_id: matchConfig.location?.id || null,
        match_type: matchConfig.matchType || 'single',
        match_mode: matchConfig.matchMode || 'past_entry',
        competitive_mode: matchConfig.competitiveMode || false,
        team_size: matchConfig.teamSize || '5v5',

        // Rule configuration fields
        match_rules: matchConfig.rules || {
          statTrackingIntensity: 'basic',
          scoringSystem: 'standard',
          useGameClock: true,
        },
        rule_preset: matchConfig.rules?.rulePreset || null,
        win_condition: matchConfig.rules?.winCondition || 'time',
        target_points: matchConfig.rules?.targetPoints || null,
        court_field_size: matchConfig.rules?.courtSize || matchConfig.rules?.fieldSize || null,
        stat_tracking_intensity: matchConfig.rules?.statTrackingIntensity || 'basic',

        // Match duration
        match_duration: matchConfig.rules?.customDuration || null,
      };

      const { data: matchResult, error: matchError } = await supabase
        .from('matches')
        .insert([matchData]);

      if (matchError) throw matchError;

      // Create a match object for navigation with enhanced data
      const match = {
        id: matchResult?.[0]?.id || Date.now().toString(),
        ...matchData,
        created_at: new Date().toISOString(),
        sport: sport,
        location: matchConfig.location,
      };

      // Navigate to appropriate screen based on match mode
      if (matchConfig.matchMode === 'real_time') {
        navigation.navigate('LiveMatch', {
          match,
          sport,
          config: matchConfig,
          players: { teamA: teamAPlayers, teamB: teamBPlayers }
        });
      } else {
        navigation.navigate('PastMatchEntry', {
          match,
          sport,
          config: matchConfig,
          players: { teamA: teamAPlayers, teamB: teamBPlayers }
        });
      }

    } catch (error) {
      console.error('Error creating match:', error);
      Alert.alert('Error', 'Failed to create match. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatIntensityLevels = () => [
    { id: 'basic', name: 'Basic', description: 'Points/goals only' },
    { id: 'intermediate', name: 'Intermediate', description: 'Adds assists, rebounds, saves, etc.' },
    { id: 'advanced', name: 'Advanced', description: 'Includes shooting %, efficiency, ratios' },
    { id: 'professional', name: 'Professional', description: 'AI-generated metrics, possession %, xG, etc.' }
  ];

  const getRulePresetName = () => {
    if (!sport) return 'Standard';
    switch (sport.name.toLowerCase()) {
      case 'basketball':
        return matchConfig.teamSize === '3v3' ? 'FIBA 3x3' : 'NBA/FIBA';
      case 'football':
        return matchConfig.teamSize === '11v11' ? 'FIFA' : 'Futsal';
      case 'volleyball':
        return 'FIVB';
      case 'table tennis':
        return 'ITTF';
      case 'badminton':
        return 'BWF';
      default:
        return 'Standard';
    }
  };

  const getMatchModeName = () => {
    const mode = getAvailableMatchModes().find(m => m.id === matchConfig.matchType);
    return mode ? mode.name : 'Single Game';
  };

  const getMatchDate = () => {
    if (matchConfig.matchMode === 'live') return 'Today';
    return matchConfig.date || 'Not selected';
  };

  const getStatIntensityName = () => {
    const level = getStatIntensityLevels().find(l => l.id === matchConfig.statIntensity);
    return level ? level.name : 'Basic';
  };

  const getValidationErrors = () => {
    const errors = [];

    if (!sport) errors.push('Sport must be selected');
    if (!matchConfig.matchMode) errors.push('Match type must be selected');
    if (!matchConfig.matchType) errors.push('Match mode must be selected');
    if (!matchConfig.teamSize) errors.push('Team size must be selected');
    if (!matchConfig.location) errors.push('Location must be selected');
    if (matchConfig.matchMode === 'log_past' && !matchConfig.date) errors.push('Date must be selected for past matches');

    const minPlayers = 1;
    if (teamAPlayers.length < minPlayers) errors.push(`Team A needs at least ${minPlayers} player(s)`);
    if (teamBPlayers.length < minPlayers) errors.push(`Team B needs at least ${minPlayers} player(s)`);

    const maxPlayers = parseInt(matchConfig.teamSize?.split('v')[0] || '5');
    if (teamAPlayers.length > maxPlayers) errors.push(`Team A has too many players (max ${maxPlayers})`);
    if (teamBPlayers.length > maxPlayers) errors.push(`Team B has too many players (max ${maxPlayers})`);

    return errors;
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Step 1: Sport + Match Type
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Select Sport + Match Type</Text>

            {/* Sport Selection */}
            <Text style={styles.sectionTitle}>1.1 Select Sport</Text>
            <Text style={styles.stepSubtitle}>
              Choose the sport you want to set up a match for
            </Text>

            {sportsLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#007AFF" />
              </View>
            ) : (
              <FlatList
                data={sports}
                numColumns={2}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.sportCard,
                      sport?.id === item.id && styles.selectedSportCard
                    ]}
                    onPress={() => setSport(item)}
                  >
                    <Ionicons
                      name={getSportIcon(item.name)}
                      size={40}
                      color={sport?.id === item.id ? '#007AFF' : '#8E8E93'}
                    />
                    <Text style={[
                      styles.sportName,
                      sport?.id === item.id && styles.selectedSportName
                    ]}>
                      {item.name}
                    </Text>
                    {sport?.id === item.id && (
                      <Ionicons name="checkmark-circle" size={20} color="#007AFF" />
                    )}
                  </TouchableOpacity>
                )}
                contentContainerStyle={styles.sportsGrid}
              />
            )}

            {/* Match Type Selection */}
            {sport && (
              <View style={styles.matchTypeSection}>
                <Text style={styles.sectionTitle}>1.2 Select Match Type</Text>

                <TouchableOpacity
                  style={[
                    styles.matchTypeOption,
                    matchConfig.matchMode === 'live' && styles.selectedMatchType
                  ]}
                  onPress={() => setMatchConfig({ ...matchConfig, matchMode: 'live' })}
                >
                  <Ionicons name="play-circle" size={32} color="#007AFF" />
                  <View style={styles.matchTypeContent}>
                    <Text style={styles.matchTypeTitle}>Live Match</Text>
                    <Text style={styles.matchTypeDescription}>
                      In-app match clock, real-time scoring, live stat tracking
                    </Text>
                  </View>
                  {matchConfig.matchMode === 'live' && (
                    <Ionicons name="checkmark-circle" size={24} color="#007AFF" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.matchTypeOption,
                    matchConfig.matchMode === 'log_past' && styles.selectedMatchType
                  ]}
                  onPress={() => setMatchConfig({ ...matchConfig, matchMode: 'log_past' })}
                >
                  <Ionicons name="document-text" size={32} color="#FF9500" />
                  <View style={styles.matchTypeContent}>
                    <Text style={styles.matchTypeTitle}>Log Past Match</Text>
                    <Text style={styles.matchTypeDescription}>
                      Manual stat entry for a completed game, no timer used
                    </Text>
                  </View>
                  {matchConfig.matchMode === 'log_past' && (
                    <Ionicons name="checkmark-circle" size={24} color="#007AFF" />
                  )}
                </TouchableOpacity>
              </View>
            )}
          </View>
        );

      case 1: // Step 2: Match Mode + Team Size + Location + Date
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Match Setup</Text>

            {/* Match Mode */}
            <Text style={styles.sectionTitle}>2.1 Match Mode</Text>
            <View style={styles.matchModeGrid}>
              {getAvailableMatchModes().map((mode) => (
                <TouchableOpacity
                  key={mode.id}
                  style={[
                    styles.matchModeCard,
                    matchConfig.matchType === mode.id && styles.selectedMatchModeCard
                  ]}
                  onPress={() => setMatchConfig({ ...matchConfig, matchType: mode.id })}
                >
                  <Text style={styles.matchModeCardTitle}>{mode.name}</Text>
                  <Text style={styles.matchModeCardDescription}>{mode.description}</Text>
                  {matchConfig.matchType === mode.id && (
                    <Ionicons name="checkmark-circle" size={20} color="#007AFF" />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* Team Size */}
            <Text style={styles.sectionTitle}>2.2 Select Team Size</Text>
            <View style={styles.teamSizeGrid}>
              {getAvailableTeamSizes().map((size) => (
                <TouchableOpacity
                  key={size}
                  style={[
                    styles.teamSizeCard,
                    matchConfig.teamSize === size && styles.selectedTeamSizeCard
                  ]}
                  onPress={() => setMatchConfig({ ...matchConfig, teamSize: size })}
                >
                  <Text style={styles.teamSizeText}>{size}</Text>
                  {matchConfig.teamSize === size && (
                    <Ionicons name="checkmark-circle" size={16} color="#007AFF" />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* Location */}
            <Text style={styles.sectionTitle}>2.3 Select Location</Text>
            <TouchableOpacity
              style={styles.locationSelector}
              onPress={() => {/* Open location picker */}}
            >
              <Ionicons name="location" size={24} color="#007AFF" />
              <Text style={styles.locationText}>
                {matchConfig.location?.name || 'Select Location'}
              </Text>
              <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
            </TouchableOpacity>

            {/* Date */}
            <Text style={styles.sectionTitle}>2.4 Set Date</Text>
            <TouchableOpacity
              style={styles.dateSelector}
              onPress={() => {/* Open date picker */}}
            >
              <Ionicons name="calendar" size={24} color="#007AFF" />
              <Text style={styles.dateText}>
                {matchConfig.matchMode === 'live' ? 'Today' : (matchConfig.date || 'Select Date')}
              </Text>
              {matchConfig.matchMode !== 'live' && (
                <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
              )}
            </TouchableOpacity>
          </View>
        );

      case 2: // Step 3: Team Setup + Players
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Team Setup + Players</Text>

            {/* Team Names */}
            <Text style={styles.sectionTitle}>3.1 Enter Team Names</Text>
            <View style={styles.teamNamesContainer}>
              <View style={styles.teamNameInput}>
                <Text style={styles.teamLabel}>Team A</Text>
                <TouchableOpacity style={styles.teamNameField}>
                  <Text style={styles.teamNameText}>{teamAName}</Text>
                  <Ionicons name="pencil" size={16} color="#8E8E93" />
                </TouchableOpacity>
              </View>

              <View style={styles.teamNameInput}>
                <Text style={styles.teamLabel}>Team B</Text>
                <TouchableOpacity style={styles.teamNameField}>
                  <Text style={styles.teamNameText}>{teamBName}</Text>
                  <Ionicons name="pencil" size={16} color="#8E8E93" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Competitive Mode Toggle */}
            <View style={styles.competitiveModeContainer}>
              <TouchableOpacity
                style={styles.competitiveModeToggle}
                onPress={() => setMatchConfig({
                  ...matchConfig,
                  competitiveMode: !matchConfig.competitiveMode
                })}
              >
                <View style={styles.toggleInfo}>
                  <Text style={styles.toggleTitle}>Competitive Mode</Text>
                  <Text style={styles.toggleDescription}>
                    Requires jersey numbers, enables leaderboard tracking
                  </Text>
                </View>
                <View style={[
                  styles.toggleSwitch,
                  matchConfig.competitiveMode && styles.toggleSwitchActive
                ]}>
                  <View style={[
                    styles.toggleKnob,
                    matchConfig.competitiveMode && styles.toggleKnobActive
                  ]} />
                </View>
              </TouchableOpacity>
            </View>

            {/* Add Players */}
            <Text style={styles.sectionTitle}>3.2 Add Players to Teams</Text>
            <View style={styles.playersContainer}>
              <View style={styles.teamPlayersSection}>
                <Text style={styles.teamPlayersTitle}>Team A Players ({teamAPlayers.length})</Text>
                <TouchableOpacity style={styles.addPlayerButton}>
                  <Ionicons name="add-circle" size={24} color="#007AFF" />
                  <Text style={styles.addPlayerText}>Add Player</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.teamPlayersSection}>
                <Text style={styles.teamPlayersTitle}>Team B Players ({teamBPlayers.length})</Text>
                <TouchableOpacity style={styles.addPlayerButton}>
                  <Ionicons name="add-circle" size={24} color="#007AFF" />
                  <Text style={styles.addPlayerText}>Add Player</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        );

      case 3: // Step 4: Configure Match Rules
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Configure Match Rules</Text>
            <Text style={styles.stepSubtitle}>
              Dynamically shown fields based on {sport?.name} and {matchConfig.teamSize}
            </Text>

            <View style={styles.rulesContainer}>
              <TouchableOpacity style={styles.rulePresetButton}>
                <Ionicons name="settings" size={24} color="#007AFF" />
                <View style={styles.rulePresetContent}>
                  <Text style={styles.rulePresetTitle}>Rule Body Preset</Text>
                  <Text style={styles.rulePresetSubtitle}>
                    {getRulePresetName()} or Custom
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
              </TouchableOpacity>

              <Text style={styles.rulesNote}>
                Configure match duration, scoring system, timeouts, fouls, and sport-specific rules
              </Text>
            </View>
          </View>
        );

      case 4: // Step 5: Select Stat Tracking Intensity
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Select Stat Tracking Intensity</Text>
            <Text style={styles.stepSubtitle}>
              Choose the level of statistics to track during the match
            </Text>

            <View style={styles.statIntensityContainer}>
              {getStatIntensityLevels().map((level) => (
                <TouchableOpacity
                  key={level.id}
                  style={[
                    styles.statIntensityCard,
                    matchConfig.statIntensity === level.id && styles.selectedStatIntensityCard
                  ]}
                  onPress={() => setMatchConfig({ ...matchConfig, statIntensity: level.id })}
                >
                  <View style={styles.statIntensityHeader}>
                    <Text style={styles.statIntensityLevel}>{level.name}</Text>
                    {matchConfig.statIntensity === level.id && (
                      <Ionicons name="checkmark-circle" size={20} color="#007AFF" />
                    )}
                  </View>
                  <Text style={styles.statIntensityDescription}>{level.description}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      case 5: // Step 6: Summary → Validation → Start/Submit
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Summary</Text>
            <Text style={styles.stepSubtitle}>
              Review your match setup and start the match
            </Text>

            <View style={styles.summaryContainer}>
              <View style={styles.summarySection}>
                <Text style={styles.summaryTitle}>Match Details</Text>
                <Text style={styles.summaryItem}>Sport: {sport?.name}</Text>
                <Text style={styles.summaryItem}>Match Type: {matchConfig.matchMode === 'live' ? 'Live Match' : 'Log Past Match'}</Text>
                <Text style={styles.summaryItem}>Mode: {getMatchModeName()}</Text>
                <Text style={styles.summaryItem}>Team Size: {matchConfig.teamSize}</Text>
                <Text style={styles.summaryItem}>Location: {matchConfig.location?.name || 'Not selected'}</Text>
                <Text style={styles.summaryItem}>Date: {getMatchDate()}</Text>
              </View>

              <View style={styles.summarySection}>
                <Text style={styles.summaryTitle}>Teams & Players</Text>
                <Text style={styles.summaryItem}>Team A: {teamAName} ({teamAPlayers.length} players)</Text>
                <Text style={styles.summaryItem}>Team B: {teamBName} ({teamBPlayers.length} players)</Text>
                <Text style={styles.summaryItem}>Competitive Mode: {matchConfig.competitiveMode ? 'Yes' : 'No'}</Text>
              </View>

              <View style={styles.summarySection}>
                <Text style={styles.summaryTitle}>Configuration</Text>
                <Text style={styles.summaryItem}>Rules: {getRulePresetName()}</Text>
                <Text style={styles.summaryItem}>Stat Intensity: {getStatIntensityName()}</Text>
              </View>

              <View style={styles.validationContainer}>
                {getValidationErrors().map((error, index) => (
                  <View key={index} style={styles.validationError}>
                    <Ionicons name="warning" size={16} color="#FF3B30" />
                    <Text style={styles.validationErrorText}>{error}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={previousStep}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Setup Match</Text>
        <Text style={styles.stepIndicator}>{currentStep + 1} of {steps.length}</Text>
      </View>

      {/* Progress Indicator */}
      <View style={styles.progressContainer}>
        {steps.map((step, index) => (
          <View key={index} style={styles.progressStep}>
            <View style={[
              styles.progressDot,
              index <= currentStep && styles.progressDotActive
            ]}>
              <Text style={[
                styles.progressNumber,
                index <= currentStep && styles.progressNumberActive
              ]}>
                {index + 1}
              </Text>
            </View>
            <Text style={[
              styles.progressLabel,
              index === currentStep && styles.progressLabelActive
            ]}>
              {step.title}
            </Text>
          </View>
        ))}
      </View>

      {/* Content */}
      {currentStep === 0 ? (
        <View style={styles.content}>
          {renderStepContent()}
        </View>
      ) : (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderStepContent()}
        </ScrollView>
      )}

      {/* Floating Action Button */}
      <View style={styles.fabContainer}>
        <TouchableOpacity
          style={[
            styles.fab,
            !validateCurrentStep() && styles.fabDisabled
          ]}
          onPress={nextStep}
          disabled={loading || !validateCurrentStep()}
        >
          <Text style={styles.fabText}>
            {currentStep === steps.length - 1 ? 'Start Match' : 'Continue'}
          </Text>
          <Ionicons
            name={currentStep === steps.length - 1 ? 'play' : 'arrow-forward'}
            size={20}
            color="white"
          />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  stepIndicator: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#F2F2F7',
  },
  progressStep: {
    alignItems: 'center',
    flex: 1,
  },
  progressDot: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E5EA',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  progressDotActive: {
    backgroundColor: '#007AFF',
  },
  progressNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: '#8E8E93',
  },
  progressNumberActive: {
    color: 'white',
  },
  progressLabel: {
    fontSize: 12,
    color: '#8E8E93',
    textAlign: 'center',
  },
  progressLabelActive: {
    color: '#007AFF',
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  stepContent: {
    padding: 20,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  stepSubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  sportsGrid: {
    paddingHorizontal: 10,
  },
  sportCard: {
    flex: 1,
    alignItems: 'center',
    padding: 20,
    margin: 8,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedSportCard: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  sportName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
    marginTop: 8,
    textAlign: 'center',
  },
  selectedSportName: {
    color: '#007AFF',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginTop: 20,
    marginBottom: 12,
  },
  matchTypeSection: {
    marginTop: 20,
  },
  matchTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedMatchType: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  matchTypeContent: {
    flex: 1,
    marginLeft: 16,
  },
  matchTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  matchTypeDescription: {
    fontSize: 14,
    color: '#8E8E93',
  },
  matchModeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  matchModeCard: {
    width: '48%',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    margin: '1%',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedMatchModeCard: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  matchModeCardTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  matchModeCardDescription: {
    fontSize: 12,
    color: '#8E8E93',
  },
  teamSizeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  teamSizeCard: {
    width: '30%',
    padding: 12,
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    margin: '1.5%',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedTeamSizeCard: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  teamSizeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  locationSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    marginBottom: 20,
  },
  locationText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#1C1C1E',
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    marginBottom: 20,
  },
  dateText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#1C1C1E',
  },
  modeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    marginBottom: 12,
  },
  selectedMode: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
    borderWidth: 2,
  },
  modeContent: {
    flex: 1,
    marginLeft: 16,
  },
  modeTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  modeDescription: {
    fontSize: 14,
    color: '#8E8E93',
  },
  reviewSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  reviewLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8E8E93',
  },
  reviewValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    flex: 1,
    textAlign: 'right',
  },
  fabContainer: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    left: 20,
  },
  fab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    borderRadius: 25,
    paddingVertical: 16,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  fabDisabled: {
    backgroundColor: '#E5E5EA',
    shadowOpacity: 0.1,
  },
  fabText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginRight: 8,
  },
  teamNamesContainer: {
    marginBottom: 20,
  },
  teamNameInput: {
    marginBottom: 16,
  },
  teamLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#1C1C1E',
  },
  teamNameField: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
  },
  teamNameText: {
    fontSize: 16,
    color: '#1C1C1E',
  },
  competitiveModeContainer: {
    marginBottom: 20,
  },
  competitiveModeToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
  },
  toggleInfo: {
    flex: 1,
  },
  toggleTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  toggleDescription: {
    fontSize: 14,
    color: '#8E8E93',
  },
  toggleSwitch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#E5E5EA',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleSwitchActive: {
    backgroundColor: '#007AFF',
  },
  toggleKnob: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  toggleKnobActive: {
    transform: [{ translateX: 20 }],
  },
  playersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  teamPlayersSection: {
    flex: 1,
    marginHorizontal: 8,
  },
  teamPlayersTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    textAlign: 'center',
  },
  addPlayerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
  },
  addPlayerText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  rulesContainer: {
    marginTop: 20,
  },
  rulePresetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    marginBottom: 16,
  },
  rulePresetContent: {
    flex: 1,
    marginLeft: 12,
  },
  rulePresetTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  rulePresetSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
  },
  rulesNote: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  statIntensityContainer: {
    marginTop: 20,
  },
  statIntensityCard: {
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedStatIntensityCard: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  statIntensityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statIntensityLevel: {
    fontSize: 16,
    fontWeight: '600',
  },
  statIntensityDescription: {
    fontSize: 14,
    color: '#8E8E93',
  },
  summaryContainer: {
    marginTop: 20,
  },
  summarySection: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#1C1C1E',
  },
  summaryItem: {
    fontSize: 14,
    marginBottom: 6,
    color: '#1C1C1E',
  },
  validationContainer: {
    marginTop: 20,
  },
  validationError: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#FFEBEE',
    borderRadius: 8,
    marginBottom: 8,
  },
  validationErrorText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#FF3B30',
  },
});

export default EnhancedMatchSetupScreen;
