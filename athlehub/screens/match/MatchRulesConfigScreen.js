import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const MatchRulesConfigScreen = ({ navigation, route }) => {
  const { sport, matchConfig, teamAName, teamBName, returnTo, nextStep } = route.params;

  // Check if this is for a past match (simplified flow)
  const isPastMatch = matchConfig.matchMode === 'past_entry';

  const [rulesConfig, setRulesConfig] = useState({
    // Timing Configuration
    matchDuration: 'standard', // 'standard', 'custom', 'unlimited'
    customDuration: 60, // minutes
    useGameClock: true,

    // Scoring Configuration
    scoringSystem: 'standard', // 'standard', 'streetball', 'custom'

    // Basketball specific
    shotClockEnabled: false,
    shotClockDuration: 24,
    quartersOrHalves: 'quarters', // 'quarters', 'halves'

    // Football specific
    extraTimeEnabled: false,
    penaltyShootoutEnabled: false,

    // Racket sports specific
    setsConfiguration: 'best_of_3', // 'best_of_3', 'best_of_5'
    pointsPerSet: 21,

    // General Rules
    timeoutsEnabled: true,
    timeoutsPerTeam: 2,
    substitutionsEnabled: true,
    maxSubstitutions: 'unlimited', // 'unlimited', '3', '5'

    // Competitive Features
    foulTrackingEnabled: matchConfig.competitiveMode,
    cardSystemEnabled: matchConfig.competitiveMode,
    overtimeEnabled: true,

    // Statistics Tracking
    statTrackingIntensity: matchConfig.competitiveMode ? 'intermediate' : 'basic',
    // 'basic', 'intermediate', 'advanced', 'professional'
  });

  useEffect(() => {
    // Set sport-specific defaults
    setSportSpecificDefaults();
  }, [sport]);

  const setSportSpecificDefaults = () => {
    const defaults = getSportDefaults(sport.name, matchConfig.teamSize);
    setRulesConfig(prev => ({ ...prev, ...defaults }));
  };

  const getSportDefaults = (sportName, teamSize) => {
    const teamSizeNum = parseInt(teamSize?.replace('v', '').split('v')[0]) || 5;

    switch (sportName.toLowerCase()) {
      case 'basketball':
        return getBasketballDefaults(teamSizeNum);
      case 'football':
        return getFootballDefaults(teamSizeNum);
      case 'badminton':
        return getBadmintonDefaults(teamSizeNum);
      case 'table tennis':
        return getTableTennisDefaults(teamSizeNum);
      case 'volleyball':
        return getVolleyballDefaults(teamSizeNum);
      default:
        return {};
    }
  };

  const getBasketballDefaults = (teamSize) => {
    switch (teamSize) {
      case 1: // 1v1
        return {
          matchDuration: 'custom',
          customDuration: 0, // First to X points
          scoringSystem: 'streetball', // 1s & 2s
          shotClockEnabled: false,
          quartersOrHalves: 'continuous',
          timeoutsPerTeam: 0,
          maxSubstitutions: '0',
          winCondition: 'first_to_points',
          targetPoints: 11,
          winByTwo: true,
          personalFoulLimit: 0,
          teamFoulLimit: 0,
          courtSize: 'half_court',
        };

      case 2:
      case 3: // 2v2, 3v3 (FIBA 3x3 style)
        return {
          matchDuration: 'custom',
          customDuration: 10,
          scoringSystem: 'fiba_3x3', // 1pt inside, 2pt outside
          shotClockEnabled: true,
          shotClockDuration: 12,
          quartersOrHalves: 'continuous',
          timeoutsPerTeam: 0,
          maxSubstitutions: 'unlimited',
          winCondition: 'time_or_points',
          targetPoints: 21,
          winByTwo: false,
          personalFoulLimit: 0,
          teamFoulLimit: 6,
          courtSize: 'half_court',
        };

      case 4: // 4v4 (Casual)
        return {
          matchDuration: 'custom',
          customDuration: 32, // 4x8 min
          scoringSystem: matchConfig.competitiveMode ? 'standard' : 'streetball',
          shotClockEnabled: matchConfig.competitiveMode,
          shotClockDuration: 24,
          quartersOrHalves: 'quarters',
          timeoutsPerTeam: 1,
          maxSubstitutions: 'unlimited',
          winCondition: 'time',
          personalFoulLimit: matchConfig.competitiveMode ? 5 : 0,
          teamFoulLimit: matchConfig.competitiveMode ? 4 : 0,
          courtSize: 'full_court',
        };

      case 5: // 5v5 (Official)
      default:
        return {
          matchDuration: 'standard',
          customDuration: matchConfig.competitiveMode ? 40 : 32, // FIBA: 4x10, Casual: 4x8
          scoringSystem: 'standard', // 2s & 3s
          shotClockEnabled: matchConfig.competitiveMode,
          shotClockDuration: 24,
          quartersOrHalves: 'quarters',
          timeoutsPerTeam: matchConfig.competitiveMode ? 5 : 2,
          maxSubstitutions: 'unlimited',
          winCondition: 'time',
          personalFoulLimit: matchConfig.competitiveMode ? 5 : 0,
          teamFoulLimit: matchConfig.competitiveMode ? 4 : 0,
          courtSize: 'full_court',
          rulePreset: matchConfig.competitiveMode ? 'fiba' : 'casual',
        };
    }
  };

  const getFootballDefaults = (teamSize) => {
    switch (teamSize) {
      case 3:
      case 4: // 3v3, 4v4 (Street/Pickup)
        return {
          matchDuration: 'custom',
          customDuration: 20, // 2x10 min
          extraTimeEnabled: false,
          penaltyShootoutEnabled: false,
          timeoutsPerTeam: 0,
          maxSubstitutions: 'unlimited',
          cardSystemEnabled: false,
          offsideEnabled: false,
          fieldSize: 'mini',
          foulLimit: 0,
        };

      case 5: // 5v5 (Futsal)
        return {
          matchDuration: 'custom',
          customDuration: 40, // 2x20 min
          extraTimeEnabled: false,
          penaltyShootoutEnabled: true,
          timeoutsPerTeam: 1,
          maxSubstitutions: 'unlimited',
          cardSystemEnabled: true,
          offsideEnabled: false,
          fieldSize: 'futsal',
          foulLimit: 5, // 5 team fouls per half
          substitutionType: 'rolling',
        };

      case 7: // 7v7 (Small-sided)
        return {
          matchDuration: 'custom',
          customDuration: 50, // 2x25 min
          extraTimeEnabled: false,
          penaltyShootoutEnabled: true,
          timeoutsPerTeam: 0,
          maxSubstitutions: 'unlimited',
          cardSystemEnabled: true,
          offsideEnabled: true,
          fieldSize: 'small',
          foulLimit: 0,
          substitutionType: 'rolling',
        };

      case 11: // 11v11 (Full FIFA)
      default:
        return {
          matchDuration: 'standard',
          customDuration: 90, // 2x45 min
          extraTimeEnabled: false,
          penaltyShootoutEnabled: false,
          timeoutsPerTeam: 0,
          maxSubstitutions: matchConfig.competitiveMode ? '5' : 'unlimited',
          cardSystemEnabled: true,
          offsideEnabled: true,
          fieldSize: 'full',
          foulLimit: 0,
          substitutionType: 'windows', // 3 substitution windows
          rulePreset: matchConfig.competitiveMode ? 'fifa' : 'casual',
        };
    }
  };

  const getBadmintonDefaults = (teamSize) => {
    return {
      setsConfiguration: 'best_of_3',
      pointsPerSet: 21,
      timeoutsPerTeam: 0,
      maxSubstitutions: '0',
      scoringSystem: 'rally',
      winByTwo: true,
      serviceRotation: 'every_2_points',
      faultEnforcement: matchConfig.competitiveMode,
      rulePreset: matchConfig.competitiveMode ? 'bwf' : 'casual',
    };
  };

  const getTableTennisDefaults = (teamSize) => {
    return {
      setsConfiguration: teamSize === 1 ? 'best_of_5' : 'best_of_3',
      pointsPerSet: 11,
      timeoutsPerTeam: 1,
      maxSubstitutions: '0',
      scoringSystem: 'rally',
      winByTwo: true,
      serviceRotation: 'every_2_serves',
      faultEnforcement: matchConfig.competitiveMode,
      rulePreset: matchConfig.competitiveMode ? 'ittf' : 'casual',
    };
  };

  const getVolleyballDefaults = (teamSize) => {
    switch (teamSize) {
      case 2: // 2v2 (Beach)
        return {
          setsConfiguration: 'best_of_3',
          pointsPerSet: 21,
          finalSetPoints: 15,
          timeoutsPerTeam: 1,
          maxSubstitutions: '0',
          scoringSystem: 'rally',
          winByTwo: true,
          courtType: 'beach',
          faultEnforcement: 'strict',
          rotationRules: false,
          rulePreset: 'fivb_beach',
        };

      case 3:
      case 4: // 3v3, 4v4 (Casual)
        return {
          setsConfiguration: 'best_of_3',
          pointsPerSet: 21,
          timeoutsPerTeam: 1,
          maxSubstitutions: 'unlimited',
          scoringSystem: 'rally',
          winByTwo: true,
          courtType: 'indoor',
          faultEnforcement: 'relaxed',
          rotationRules: false,
        };

      case 6: // 6v6 (Official)
      default:
        return {
          setsConfiguration: 'best_of_5',
          pointsPerSet: 25,
          finalSetPoints: 15,
          timeoutsPerTeam: 2,
          maxSubstitutions: matchConfig.competitiveMode ? '6' : 'unlimited',
          scoringSystem: 'rally',
          winByTwo: true,
          courtType: 'indoor',
          faultEnforcement: matchConfig.competitiveMode ? 'strict' : 'relaxed',
          rotationRules: true,
          substitutionEntries: 12, // 12 total entries per set
          rulePreset: matchConfig.competitiveMode ? 'fivb_indoor' : 'casual',
        };
    }
  };

  const getStatTrackingDescription = (intensity) => {
    switch (intensity) {
      case 'basic':
        return 'Essential stats only (points, goals, basic match data)';
      case 'intermediate':
        return 'Key gameplay stats (assists, rebounds, shots, etc.)';
      case 'advanced':
        return 'Detailed efficiency metrics and calculated percentages';
      case 'professional':
        return 'Comprehensive analytics with advanced performance metrics';
      default:
        return '';
    }
  };

  const proceedToPlayerSetup = () => {
    const finalConfig = {
      ...matchConfig,
      rules: rulesConfig,
    };

    if (returnTo === 'EnhancedMatchSetup') {
      // Return to the enhanced setup flow with updated config
      navigation.navigate('EnhancedMatchSetup', {
        sport,
        initialConfig: finalConfig,
        teamAName,
        teamBName,
        startAtStep: nextStep || 2,
      });
    } else {
      // Direct navigation to player setup (fallback)
      navigation.navigate('PlayerSetup', {
        sport,
        matchConfig: finalConfig,
        teamAName,
        teamBName,
      });
    }
  };

  const ConfigSection = ({ title, children }) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const ConfigOption = ({ title, subtitle, value, onValueChange, type = 'switch' }) => (
    <View style={styles.configOption}>
      <View style={styles.optionText}>
        <Text style={styles.optionTitle}>{title}</Text>
        {subtitle && <Text style={styles.optionSubtitle}>{subtitle}</Text>}
      </View>
      {type === 'switch' && (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#E5E5EA', true: '#007AFF' }}
          thumbColor="white"
        />
      )}
    </View>
  );

  const SelectionOption = ({ title, options, selectedValue, onSelect }) => (
    <View style={styles.selectionSection}>
      <Text style={styles.selectionTitle}>{title}</Text>
      <View style={styles.optionsContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.optionButton,
              selectedValue === option.value && styles.selectedOptionButton
            ]}
            onPress={() => onSelect(option.value)}
          >
            <Text style={[
              styles.optionButtonText,
              selectedValue === option.value && styles.selectedOptionButtonText
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderBasketballConfig = () => {
    const teamSizeNum = parseInt(matchConfig.teamSize?.replace('v', '').split('v')[0]) || 5;

    return (
      <>
        {/* Rule Preset Selection */}
        {teamSizeNum === 5 && matchConfig.competitiveMode && (
          <SelectionOption
            title="Official Rules"
            options={[
              { value: 'fiba', label: 'FIBA (4×10 min)' },
              { value: 'nba', label: 'NBA (4×12 min)' },
              { value: 'ncaa', label: 'NCAA (2×20 min)' },
            ]}
            selectedValue={rulesConfig.rulePreset}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, rulePreset: value }))}
          />
        )}

        {/* Win Condition for 1v1 and 3x3 */}
        {(teamSizeNum <= 3) && (
          <SelectionOption
            title="Win Condition"
            options={[
              { value: 'first_to_points', label: `First to ${rulesConfig.targetPoints}` },
              { value: 'time_or_points', label: `${rulesConfig.customDuration} min or ${rulesConfig.targetPoints} pts` },
              { value: 'time', label: `${rulesConfig.customDuration} minutes` },
            ]}
            selectedValue={rulesConfig.winCondition}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, winCondition: value }))}
          />
        )}

        {/* Game Format */}
        {teamSizeNum >= 4 && (
          <SelectionOption
            title="Game Format"
            options={[
              { value: 'quarters', label: '4 Quarters' },
              { value: 'halves', label: '2 Halves' },
              { value: 'continuous', label: 'Continuous Play' },
            ]}
            selectedValue={rulesConfig.quartersOrHalves}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, quartersOrHalves: value }))}
          />
        )}

        {/* Scoring System */}
        <SelectionOption
          title="Scoring System"
          options={[
            { value: 'standard', label: '2s & 3s' },
            { value: 'streetball', label: '1s & 2s' },
            { value: 'fiba_3x3', label: '1pt inside, 2pt outside' },
          ]}
          selectedValue={rulesConfig.scoringSystem}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, scoringSystem: value }))}
        />

        {/* Court Size */}
        <SelectionOption
          title="Court Size"
          options={[
            { value: 'half_court', label: 'Half Court' },
            { value: 'full_court', label: 'Full Court' },
          ]}
          selectedValue={rulesConfig.courtSize}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, courtSize: value }))}
        />

        {/* Shot Clock */}
        {teamSizeNum >= 2 && (
          <ConfigOption
            title="Shot Clock"
            subtitle={`${rulesConfig.shotClockDuration} seconds per possession`}
            value={rulesConfig.shotClockEnabled}
            onValueChange={(value) => setRulesConfig(prev => ({ ...prev, shotClockEnabled: value }))}
          />
        )}

        {/* Foul Limits */}
        {matchConfig.competitiveMode && teamSizeNum >= 3 && (
          <>
            {rulesConfig.personalFoulLimit > 0 && (
              <ConfigOption
                title="Personal Fouls"
                subtitle={`${rulesConfig.personalFoulLimit} fouls before disqualification`}
                value={rulesConfig.personalFoulLimit > 0}
                onValueChange={(value) => setRulesConfig(prev => ({
                  ...prev,
                  personalFoulLimit: value ? 5 : 0
                }))}
              />
            )}

            {rulesConfig.teamFoulLimit > 0 && (
              <ConfigOption
                title="Team Fouls"
                subtitle={`Bonus free throws after ${rulesConfig.teamFoulLimit} team fouls`}
                value={rulesConfig.teamFoulLimit > 0}
                onValueChange={(value) => setRulesConfig(prev => ({
                  ...prev,
                  teamFoulLimit: value ? (teamSizeNum === 3 ? 6 : 4) : 0
                }))}
              />
            )}
          </>
        )}
      </>
    );
  };

  const renderFootballConfig = () => {
    const teamSizeNum = parseInt(matchConfig.teamSize?.replace('v', '').split('v')[0]) || 11;

    return (
      <>
        {/* Rule Preset Selection */}
        {teamSizeNum === 11 && matchConfig.competitiveMode && (
          <SelectionOption
            title="Official Rules"
            options={[
              { value: 'fifa', label: 'FIFA 11v11' },
              { value: 'casual', label: 'Casual Play' },
            ]}
            selectedValue={rulesConfig.rulePreset}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, rulePreset: value }))}
          />
        )}

        {teamSizeNum === 5 && (
          <SelectionOption
            title="Futsal Rules"
            options={[
              { value: 'fifa_futsal', label: 'FIFA Futsal' },
              { value: 'casual', label: 'Casual 5v5' },
            ]}
            selectedValue={rulesConfig.rulePreset}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, rulePreset: value }))}
          />
        )}

        {/* Field Size */}
        <SelectionOption
          title="Field Size"
          options={[
            { value: 'mini', label: 'Mini Field' },
            { value: 'futsal', label: 'Futsal Court' },
            { value: 'small', label: 'Small Pitch' },
            { value: 'full', label: 'Full Pitch' },
          ]}
          selectedValue={rulesConfig.fieldSize}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, fieldSize: value }))}
        />

        {/* Substitutions */}
        <SelectionOption
          title="Substitutions"
          options={[
            { value: 'unlimited', label: 'Unlimited' },
            { value: '3', label: '3 per team' },
            { value: '5', label: '5 per team' },
          ]}
          selectedValue={rulesConfig.maxSubstitutions}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, maxSubstitutions: value }))}
        />

        {/* Substitution Type for 5v5+ */}
        {teamSizeNum >= 5 && (
          <SelectionOption
            title="Substitution Type"
            options={[
              { value: 'rolling', label: 'On-the-fly' },
              { value: 'windows', label: 'During breaks' },
            ]}
            selectedValue={rulesConfig.substitutionType}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, substitutionType: value }))}
          />
        )}

        {/* Card System */}
        {teamSizeNum >= 5 && (
          <ConfigOption
            title="Card System"
            subtitle="Yellow and red card enforcement"
            value={rulesConfig.cardSystemEnabled}
            onValueChange={(value) => setRulesConfig(prev => ({ ...prev, cardSystemEnabled: value }))}
          />
        )}

        {/* Offside Rules */}
        {teamSizeNum >= 7 && (
          <ConfigOption
            title="Offside Rules"
            subtitle="Enforce offside violations"
            value={rulesConfig.offsideEnabled}
            onValueChange={(value) => setRulesConfig(prev => ({ ...prev, offsideEnabled: value }))}
          />
        )}

        {/* Foul Limit for Futsal */}
        {teamSizeNum === 5 && rulesConfig.foulLimit > 0 && (
          <ConfigOption
            title="Team Foul Limit"
            subtitle={`${rulesConfig.foulLimit} fouls per half = penalty kicks`}
            value={rulesConfig.foulLimit > 0}
            onValueChange={(value) => setRulesConfig(prev => ({ ...prev, foulLimit: value ? 5 : 0 }))}
          />
        )}

        {/* Extra Time and Penalties */}
        {matchConfig.competitiveMode && teamSizeNum >= 7 && (
          <>
            <ConfigOption
              title="Extra Time"
              subtitle="30 minutes additional time if tied"
              value={rulesConfig.extraTimeEnabled}
              onValueChange={(value) => setRulesConfig(prev => ({ ...prev, extraTimeEnabled: value }))}
            />

            <ConfigOption
              title="Penalty Shootout"
              subtitle="Decide winner if still tied after extra time"
              value={rulesConfig.penaltyShootoutEnabled}
              onValueChange={(value) => setRulesConfig(prev => ({ ...prev, penaltyShootoutEnabled: value }))}
            />
          </>
        )}
      </>
    );
  };

  const renderRacketSportsConfig = () => {
    const teamSizeNum = parseInt(matchConfig.teamSize?.replace('v', '').split('v')[0]) || 1;
    const isBadminton = sport.name.toLowerCase() === 'badminton';
    const isTableTennis = sport.name.toLowerCase() === 'table tennis';

    return (
      <>
        {/* Rule Preset Selection */}
        {matchConfig.competitiveMode && (
          <SelectionOption
            title="Official Rules"
            options={[
              {
                value: isBadminton ? 'bwf' : 'ittf',
                label: isBadminton ? 'BWF Rules' : 'ITTF Rules'
              },
              { value: 'casual', label: 'Casual Play' },
            ]}
            selectedValue={rulesConfig.rulePreset}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, rulePreset: value }))}
          />
        )}

        {/* Match Format */}
        <SelectionOption
          title="Match Format"
          options={[
            { value: 'best_of_3', label: 'Best of 3 Sets' },
            { value: 'best_of_5', label: 'Best of 5 Sets' },
            { value: 'best_of_7', label: 'Best of 7 Sets' },
          ]}
          selectedValue={rulesConfig.setsConfiguration}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, setsConfiguration: value }))}
        />

        {/* Points per Set */}
        <SelectionOption
          title="Points per Set"
          options={[
            { value: 11, label: '11 Points' },
            { value: 15, label: '15 Points' },
            { value: 21, label: '21 Points' },
          ]}
          selectedValue={rulesConfig.pointsPerSet}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, pointsPerSet: value }))}
        />

        {/* Scoring System */}
        <SelectionOption
          title="Scoring System"
          options={[
            { value: 'rally', label: 'Rally Scoring' },
            { value: 'traditional', label: 'Traditional (serve to score)' },
          ]}
          selectedValue={rulesConfig.scoringSystem}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, scoringSystem: value }))}
        />

        {/* Win by 2 Rule */}
        <ConfigOption
          title="Win by 2 Rule"
          subtitle="Must win set by at least 2 points"
          value={rulesConfig.winByTwo}
          onValueChange={(value) => setRulesConfig(prev => ({ ...prev, winByTwo: value }))}
        />

        {/* Service Rotation */}
        <SelectionOption
          title="Service Rotation"
          options={[
            { value: 'every_2_points', label: 'Every 2 Points' },
            { value: 'every_2_serves', label: 'Every 2 Serves' },
            { value: 'every_5_serves', label: 'Every 5 Serves' },
          ]}
          selectedValue={rulesConfig.serviceRotation}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, serviceRotation: value }))}
        />

        {/* Fault Enforcement */}
        <ConfigOption
          title="Fault Enforcement"
          subtitle={`Enforce ${isBadminton ? 'net touches, service faults' : 'edge calls, illegal serves'}`}
          value={rulesConfig.faultEnforcement}
          onValueChange={(value) => setRulesConfig(prev => ({ ...prev, faultEnforcement: value }))}
        />

        {/* Doubles-specific rules */}
        {teamSizeNum === 2 && (
          <>
            <ConfigOption
              title="Doubles Service Order"
              subtitle="Enforce strict service rotation in doubles"
              value={rulesConfig.doublesServiceOrder || false}
              onValueChange={(value) => setRulesConfig(prev => ({ ...prev, doublesServiceOrder: value }))}
            />

            {isBadminton && (
              <ConfigOption
                title="Doubles Court Lines"
                subtitle="Use doubles court boundaries"
                value={rulesConfig.doublesCourtLines || true}
                onValueChange={(value) => setRulesConfig(prev => ({ ...prev, doublesCourtLines: value }))}
              />
            )}
          </>
        )}
      </>
    );
  };

  const renderVolleyballConfig = () => {
    const teamSizeNum = parseInt(matchConfig.teamSize?.replace('v', '').split('v')[0]) || 6;

    return (
      <>
        {/* Rule Preset Selection */}
        {teamSizeNum === 6 && matchConfig.competitiveMode && (
          <SelectionOption
            title="Official Rules"
            options={[
              { value: 'fivb_indoor', label: 'FIVB Indoor' },
              { value: 'casual', label: 'Casual Play' },
            ]}
            selectedValue={rulesConfig.rulePreset}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, rulePreset: value }))}
          />
        )}

        {teamSizeNum === 2 && (
          <SelectionOption
            title="Beach Volleyball"
            options={[
              { value: 'fivb_beach', label: 'FIVB Beach' },
              { value: 'casual', label: 'Casual Beach' },
            ]}
            selectedValue={rulesConfig.rulePreset}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, rulePreset: value }))}
          />
        )}

        {/* Court Type */}
        <SelectionOption
          title="Court Type"
          options={[
            { value: 'indoor', label: 'Indoor Court' },
            { value: 'beach', label: 'Beach/Sand' },
            { value: 'grass', label: 'Grass Court' },
          ]}
          selectedValue={rulesConfig.courtType}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, courtType: value }))}
        />

        {/* Match Format */}
        <SelectionOption
          title="Match Format"
          options={[
            { value: 'best_of_3', label: 'Best of 3 Sets' },
            { value: 'best_of_5', label: 'Best of 5 Sets' },
          ]}
          selectedValue={rulesConfig.setsConfiguration}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, setsConfiguration: value }))}
        />

        {/* Points per Set */}
        <SelectionOption
          title="Points per Set"
          options={[
            { value: 21, label: '21 Points' },
            { value: 25, label: '25 Points' },
          ]}
          selectedValue={rulesConfig.pointsPerSet}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, pointsPerSet: value }))}
        />

        {/* Final Set Points (for best of 5) */}
        {rulesConfig.setsConfiguration === 'best_of_5' && (
          <SelectionOption
            title="Final Set Points"
            options={[
              { value: 15, label: '15 Points' },
              { value: 21, label: '21 Points' },
            ]}
            selectedValue={rulesConfig.finalSetPoints}
            onSelect={(value) => setRulesConfig(prev => ({ ...prev, finalSetPoints: value }))}
          />
        )}

        {/* Scoring System */}
        <SelectionOption
          title="Scoring System"
          options={[
            { value: 'rally', label: 'Rally Scoring' },
            { value: 'sideout', label: 'Side-out Scoring' },
          ]}
          selectedValue={rulesConfig.scoringSystem}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, scoringSystem: value }))}
        />

        {/* Win by 2 Rule */}
        <ConfigOption
          title="Win by 2 Rule"
          subtitle="Must win set by at least 2 points"
          value={rulesConfig.winByTwo}
          onValueChange={(value) => setRulesConfig(prev => ({ ...prev, winByTwo: value }))}
        />

        {/* Rotation Rules */}
        {teamSizeNum >= 4 && (
          <ConfigOption
            title="Rotation Rules"
            subtitle="Enforce player rotation positions"
            value={rulesConfig.rotationRules}
            onValueChange={(value) => setRulesConfig(prev => ({ ...prev, rotationRules: value }))}
          />
        )}

        {/* Fault Enforcement */}
        <SelectionOption
          title="Fault Enforcement"
          options={[
            { value: 'strict', label: 'Strict (All faults)' },
            { value: 'relaxed', label: 'Relaxed (Major faults only)' },
            { value: 'none', label: 'No fault calls' },
          ]}
          selectedValue={rulesConfig.faultEnforcement}
          onSelect={(value) => setRulesConfig(prev => ({ ...prev, faultEnforcement: value }))}
        />

        {/* Substitution Entries (6v6 only) */}
        {teamSizeNum === 6 && matchConfig.competitiveMode && (
          <ConfigOption
            title="Substitution Tracking"
            subtitle={`Track ${rulesConfig.substitutionEntries} total entries per set`}
            value={rulesConfig.substitutionEntries > 0}
            onValueChange={(value) => setRulesConfig(prev => ({
              ...prev,
              substitutionEntries: value ? 12 : 0
            }))}
          />
        )}
      </>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Match Rules</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {isPastMatch ? (
          // Simplified configuration for past matches
          <>
            <ConfigSection title="Match Statistics">
              <Text style={styles.sectionDescription}>
                Configure how detailed you want the statistics tracking for this past match.
              </Text>

              <SelectionOption
                title="Statistics Detail Level"
                options={[
                  { value: 'basic', label: 'Basic' },
                  { value: 'intermediate', label: 'Intermediate' },
                  { value: 'advanced', label: 'Advanced' },
                ]}
                selectedValue={rulesConfig.statTrackingIntensity}
                onSelect={(value) => setRulesConfig(prev => ({ ...prev, statTrackingIntensity: value }))}
              />

              <View style={styles.intensityDescription}>
                <Text style={styles.intensityText}>
                  {getStatTrackingDescription(rulesConfig.statTrackingIntensity)}
                </Text>
              </View>
            </ConfigSection>

            <View style={styles.summarySection}>
              <Text style={styles.summaryTitle}>What You'll Track</Text>
              <Text style={styles.summaryText}>
                • Final scores and match result
              </Text>
              <Text style={styles.summaryText}>
                • {rulesConfig.statTrackingIntensity} level player statistics
              </Text>
              <Text style={styles.summaryText}>
                • Match date and duration
              </Text>
            </View>
          </>
        ) : (
          // Full configuration for real-time matches
          <>
            {/* Sport-Specific Configuration */}
            <ConfigSection title={`${sport.name} Configuration`}>
              {sport.name.toLowerCase() === 'basketball' && renderBasketballConfig()}
              {sport.name.toLowerCase() === 'football' && renderFootballConfig()}
              {(sport.name.toLowerCase() === 'badminton' || sport.name.toLowerCase() === 'table tennis') && renderRacketSportsConfig()}
              {sport.name.toLowerCase() === 'volleyball' && renderVolleyballConfig()}
            </ConfigSection>

            {/* General Rules */}
            <ConfigSection title="General Rules">
              <ConfigOption
                title="Game Timer"
                subtitle="Track match duration with live timer"
                value={rulesConfig.useGameClock}
                onValueChange={(value) => setRulesConfig(prev => ({ ...prev, useGameClock: value }))}
              />

              {rulesConfig.timeoutsPerTeam > 0 && (
                <ConfigOption
                  title="Timeouts"
                  subtitle={`${rulesConfig.timeoutsPerTeam} per team`}
                  value={rulesConfig.timeoutsEnabled}
                  onValueChange={(value) => setRulesConfig(prev => ({ ...prev, timeoutsEnabled: value }))}
                />
              )}

              <ConfigOption
                title="Overtime"
                subtitle="Additional time if match is tied"
                value={rulesConfig.overtimeEnabled}
                onValueChange={(value) => setRulesConfig(prev => ({ ...prev, overtimeEnabled: value }))}
              />
            </ConfigSection>

            {/* Statistics Tracking */}
            <ConfigSection title="Statistics Tracking">
              <SelectionOption
                title="Tracking Intensity"
                options={[
                  { value: 'basic', label: 'Basic' },
                  { value: 'intermediate', label: 'Intermediate' },
                  { value: 'advanced', label: 'Advanced' },
                  { value: 'professional', label: 'Professional' },
                ]}
                selectedValue={rulesConfig.statTrackingIntensity}
                onSelect={(value) => setRulesConfig(prev => ({ ...prev, statTrackingIntensity: value }))}
              />

              <View style={styles.intensityDescription}>
                <Text style={styles.intensityText}>
                  {getStatTrackingDescription(rulesConfig.statTrackingIntensity)}
                </Text>
              </View>
            </ConfigSection>

            {/* Competitive Features */}
            {matchConfig.competitiveMode && (
              <ConfigSection title="Competitive Features">
                <ConfigOption
                  title="Foul Tracking"
                  subtitle="Track personal and team fouls"
                  value={rulesConfig.foulTrackingEnabled}
                  onValueChange={(value) => setRulesConfig(prev => ({ ...prev, foulTrackingEnabled: value }))}
                />

                {(sport.name.toLowerCase() === 'football') && (
                  <ConfigOption
                    title="Card System"
                    subtitle="Yellow and red card enforcement"
                    value={rulesConfig.cardSystemEnabled}
                    onValueChange={(value) => setRulesConfig(prev => ({ ...prev, cardSystemEnabled: value }))}
                  />
                )}
              </ConfigSection>
            )}

            {/* Configuration Summary */}
            <View style={styles.summarySection}>
              <Text style={styles.summaryTitle}>Configuration Summary</Text>
              <Text style={styles.summaryText}>
                • {matchConfig.competitiveMode ? 'Competitive' : 'Casual'} mode
              </Text>
              <Text style={styles.summaryText}>
                • {rulesConfig.statTrackingIntensity} statistics tracking
              </Text>
              <Text style={styles.summaryText}>
                • {rulesConfig.useGameClock ? 'Timed' : 'Untimed'} match
              </Text>
              {sport.name.toLowerCase() === 'basketball' && (
                <Text style={styles.summaryText}>
                  • {rulesConfig.scoringSystem === 'standard' ? '2s & 3s' : '1s & 2s'} scoring
                </Text>
              )}
            </View>
          </>
        )}
      </ScrollView>

      {/* Continue Button */}
      <View style={styles.continueContainer}>
        <TouchableOpacity style={styles.continueButton} onPress={proceedToPlayerSetup}>
          <Text style={styles.continueButtonText}>Continue to Player Setup</Text>
          <Ionicons name="arrow-forward" size={20} color="white" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#1C1C1E',
  },
  sectionDescription: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 16,
    lineHeight: 20,
  },
  configOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  optionText: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
  },
  optionSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 2,
  },
  selectionSection: {
    marginBottom: 20,
  },
  selectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#1C1C1E',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F2F2F7',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedOptionButton: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  optionButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E8E93',
  },
  selectedOptionButtonText: {
    color: '#007AFF',
  },
  intensityDescription: {
    backgroundColor: '#F2F2F7',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  intensityText: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 20,
  },
  summarySection: {
    padding: 20,
    backgroundColor: '#F2F2F7',
    margin: 20,
    borderRadius: 12,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#1C1C1E',
  },
  summaryText: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 4,
  },
  continueContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 16,
  },
  continueButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginRight: 8,
  },
});

export default MatchRulesConfigScreen;
