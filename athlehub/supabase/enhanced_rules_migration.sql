-- Enhanced Match Rules Migration
-- Adds comprehensive rule configuration storage to support the enhanced match setup system

-- 1. Add match_rules column to store comprehensive rule configurations
ALTER TABLE public.matches
ADD COLUMN IF NOT EXISTS match_rules JSONB;

-- 2. Add team_size column to track team configuration
ALTER TABLE public.matches
ADD COLUMN IF NOT EXISTS team_size TEXT DEFAULT '5v5';

-- 3. Add rule_preset column to track which official rules were used
ALTER TABLE public.matches
ADD COLUMN IF NOT EXISTS rule_preset TEXT;

-- 4. Add win_condition column for matches with different win criteria
ALTER TABLE public.matches
ADD COLUMN IF NOT EXISTS win_condition TEXT DEFAULT 'time' CHECK (win_condition IN ('time', 'first_to_points', 'time_or_points', 'sets'));

-- 5. Add target_points for point-based win conditions
ALTER TABLE public.matches
ADD COLUMN IF NOT EXISTS target_points INTEGER;

-- 6. Add court_field_size to track playing area
ALTER TABLE public.matches
ADD COLUMN IF NOT EXISTS court_field_size TEXT;

-- 7. Add stat_tracking_intensity to track what level of stats were recorded
ALTER TABLE public.matches
ADD COLUMN IF NOT EXISTS stat_tracking_intensity TEXT DEFAULT 'basic' CHECK (stat_tracking_intensity IN ('basic', 'intermediate', 'advanced', 'professional'));

-- 8. Update existing matches to have default rule configurations
UPDATE public.matches 
SET 
  match_rules = '{"scoringSystem": "standard", "useGameClock": true, "statTrackingIntensity": "basic"}',
  team_size = '5v5',
  stat_tracking_intensity = 'basic'
WHERE match_rules IS NULL;

-- 9. Create index for rule-based queries
CREATE INDEX IF NOT EXISTS idx_matches_rule_preset ON public.matches(rule_preset);
CREATE INDEX IF NOT EXISTS idx_matches_team_size ON public.matches(team_size);
CREATE INDEX IF NOT EXISTS idx_matches_stat_tracking_intensity ON public.matches(stat_tracking_intensity);

-- 10. Add comments for documentation
COMMENT ON COLUMN public.matches.match_rules IS 'JSONB field storing comprehensive match rule configuration including timing, scoring, fouls, substitutions, and sport-specific rules';
COMMENT ON COLUMN public.matches.team_size IS 'Team size configuration (e.g., 1v1, 2v2, 3v3, 5v5, 7v7, 11v11)';
COMMENT ON COLUMN public.matches.rule_preset IS 'Official rule preset used (e.g., fiba, nba, fifa, fivb_indoor, bwf, ittf)';
COMMENT ON COLUMN public.matches.win_condition IS 'How the match winner is determined (time-based, points-based, or hybrid)';
COMMENT ON COLUMN public.matches.target_points IS 'Target points for point-based win conditions (e.g., first to 11, 21)';
COMMENT ON COLUMN public.matches.court_field_size IS 'Playing area size (e.g., half_court, full_court, mini_field, futsal)';
COMMENT ON COLUMN public.matches.stat_tracking_intensity IS 'Level of statistical detail tracked during the match';

-- 11. Create a view for enhanced match queries with rule information
CREATE OR REPLACE VIEW public.enhanced_matches AS
SELECT 
  m.*,
  s.name as sport_name,
  l.name as location_name,
  -- Extract commonly used rule fields from JSONB
  (m.match_rules->>'scoringSystem') as scoring_system,
  (m.match_rules->>'shotClockEnabled')::boolean as shot_clock_enabled,
  (m.match_rules->>'shotClockDuration')::integer as shot_clock_duration,
  (m.match_rules->>'quartersOrHalves') as game_format,
  (m.match_rules->>'personalFoulLimit')::integer as personal_foul_limit,
  (m.match_rules->>'teamFoulLimit')::integer as team_foul_limit,
  (m.match_rules->>'maxSubstitutions') as max_substitutions,
  (m.match_rules->>'timeoutsPerTeam')::integer as timeouts_per_team,
  (m.match_rules->>'setsConfiguration') as sets_configuration,
  (m.match_rules->>'pointsPerSet')::integer as points_per_set,
  (m.match_rules->>'winByTwo')::boolean as win_by_two,
  (m.match_rules->>'cardSystemEnabled')::boolean as card_system_enabled,
  (m.match_rules->>'offsideEnabled')::boolean as offside_enabled,
  (m.match_rules->>'faultEnforcement') as fault_enforcement
FROM public.matches m
LEFT JOIN public.sports s ON m.sport_id = s.id
LEFT JOIN public.locations l ON m.location_id = l.id;

-- 12. Create function to validate match rules JSONB structure
CREATE OR REPLACE FUNCTION validate_match_rules(rules JSONB)
RETURNS BOOLEAN AS $$
BEGIN
  -- Basic validation - ensure required fields exist
  IF rules IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Check for required fields
  IF NOT (rules ? 'statTrackingIntensity') THEN
    RETURN FALSE;
  END IF;
  
  -- Validate stat tracking intensity values
  IF NOT (rules->>'statTrackingIntensity' IN ('basic', 'intermediate', 'advanced', 'professional')) THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 13. Add constraint to ensure valid match rules
ALTER TABLE public.matches
ADD CONSTRAINT valid_match_rules CHECK (validate_match_rules(match_rules));

-- 14. Create function to get rule summary for a match
CREATE OR REPLACE FUNCTION get_match_rule_summary(match_id UUID)
RETURNS TEXT AS $$
DECLARE
  match_record RECORD;
  rule_summary TEXT;
BEGIN
  SELECT * INTO match_record FROM public.enhanced_matches WHERE id = match_id;
  
  IF NOT FOUND THEN
    RETURN 'Match not found';
  END IF;
  
  rule_summary := format('%s %s match', match_record.team_size, match_record.sport_name);
  
  IF match_record.rule_preset IS NOT NULL THEN
    rule_summary := rule_summary || format(' (%s rules)', UPPER(match_record.rule_preset));
  END IF;
  
  IF match_record.competitive_mode THEN
    rule_summary := rule_summary || ' - Competitive';
  ELSE
    rule_summary := rule_summary || ' - Casual';
  END IF;
  
  rule_summary := rule_summary || format(' - %s stats', match_record.stat_tracking_intensity);
  
  RETURN rule_summary;
END;
$$ LANGUAGE plpgsql;

-- 15. Grant necessary permissions
GRANT SELECT ON public.enhanced_matches TO authenticated;
GRANT EXECUTE ON FUNCTION validate_match_rules(JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_match_rule_summary(UUID) TO authenticated;
