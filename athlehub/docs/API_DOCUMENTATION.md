# 🔌 Athlehub API Documentation

## Overview
This document outlines the enhanced database schema and API structure for Athlehub V2.5, including the comprehensive match setup system, professional rule configurations, and location-based features.

## 📊 Database Schema

### Enhanced Matches Table
```sql
CREATE TABLE public.matches (
  -- Core fields
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  sport_id INTEGER REFERENCES public.sports(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Basic match data
  team_a_name TEXT NOT NULL,
  team_b_name TEXT NOT NULL,
  team_a_score INTEGER DEFAULT 0,
  team_b_score INTEGER DEFAULT 0,
  match_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Enhanced V2.5 fields
  location_id INTEGER REFERENCES public.locations(id),
  match_type TEXT DEFAULT 'single' CHECK (match_type IN ('single', 'set_based', 'tournament', 'rotational')),
  match_mode TEXT DEFAULT 'past_entry' CHECK (match_mode IN ('real_time', 'past_entry')),
  competitive_mode BOOLEAN DEFAULT FALSE,
  team_size TEXT DEFAULT '5v5',
  
  -- Rule configuration
  match_rules JSONB,
  rule_preset TEXT,
  win_condition TEXT DEFAULT 'time' CHECK (win_condition IN ('time', 'first_to_points', 'time_or_points', 'sets')),
  target_points INTEGER,
  court_field_size TEXT,
  stat_tracking_intensity TEXT DEFAULT 'basic' CHECK (stat_tracking_intensity IN ('basic', 'intermediate', 'advanced', 'professional')),
  match_duration INTEGER, -- in minutes
  
  -- Status tracking
  status TEXT DEFAULT 'completed' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled'))
);
```

### Enhanced Sports Table
```sql
CREATE TABLE public.sports (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  icon TEXT,
  
  -- Enhanced V2.5 fields
  supported_team_sizes TEXT[] DEFAULT ARRAY['5v5'],
  supported_match_types TEXT[] DEFAULT ARRAY['single'],
  default_match_duration INTEGER DEFAULT 40,
  supports_sets BOOLEAN DEFAULT FALSE,
  default_scoring_system TEXT DEFAULT 'standard',
  available_rule_presets TEXT[] DEFAULT ARRAY[]::TEXT[]
);
```

### Locations Table
```sql
CREATE TABLE public.locations (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  address TEXT,
  city TEXT,
  state TEXT,
  country TEXT DEFAULT 'US',
  
  -- Enhanced fields
  sport_types TEXT[] DEFAULT ARRAY[]::TEXT[],
  court_type TEXT, -- 'indoor', 'outdoor', 'beach', 'grass'
  surface_type TEXT, -- 'hardwood', 'concrete', 'sand', 'grass', 'synthetic'
  capacity INTEGER,
  amenities TEXT[],
  
  -- Coordinates for future GPS features
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🎯 Match Rules JSONB Structure

### Basketball Rules Example
```json
{
  "rulePreset": "fiba",
  "matchDuration": 40,
  "quartersOrHalves": "quarters",
  "scoringSystem": "standard",
  "shotClockEnabled": true,
  "shotClockDuration": 24,
  "personalFoulLimit": 5,
  "teamFoulLimit": 4,
  "timeoutsPerTeam": 5,
  "maxSubstitutions": "unlimited",
  "courtSize": "full_court",
  "winCondition": "time",
  "statTrackingIntensity": "intermediate",
  "overtimeEnabled": false,
  "overtimeDuration": 5
}
```

### Football Rules Example
```json
{
  "rulePreset": "fifa_futsal",
  "matchDuration": 40,
  "fieldSize": "futsal",
  "cardSystemEnabled": true,
  "offsideEnabled": false,
  "substitutionType": "rolling",
  "maxSubstitutions": "unlimited",
  "foulLimit": 5,
  "penaltyShootoutEnabled": true,
  "extraTimeEnabled": false,
  "statTrackingIntensity": "intermediate"
}
```

### Volleyball Rules Example
```json
{
  "rulePreset": "fivb_indoor",
  "setsConfiguration": "best_of_5",
  "pointsPerSet": 25,
  "finalSetPoints": 15,
  "scoringSystem": "rally",
  "winByTwo": true,
  "courtType": "indoor",
  "rotationRules": true,
  "maxSubstitutions": "6",
  "substitutionEntries": 12,
  "faultEnforcement": "strict",
  "timeoutsPerTeam": 2,
  "statTrackingIntensity": "advanced"
}
```

## 🔧 API Endpoints

### Match Creation
```javascript
// Enhanced match creation with comprehensive rule configuration
const createMatch = async (matchData) => {
  const { data, error } = await supabase
    .from('matches')
    .insert([{
      user_id: user.id,
      sport_id: matchData.sport.id,
      team_a_name: matchData.teamAName,
      team_b_name: matchData.teamBName,
      location_id: matchData.location?.id,
      match_type: matchData.matchType,
      match_mode: matchData.matchMode,
      competitive_mode: matchData.competitiveMode,
      team_size: matchData.teamSize,
      match_rules: matchData.rules,
      rule_preset: matchData.rules?.rulePreset,
      win_condition: matchData.rules?.winCondition,
      target_points: matchData.rules?.targetPoints,
      court_field_size: matchData.rules?.courtSize || matchData.rules?.fieldSize,
      stat_tracking_intensity: matchData.rules?.statTrackingIntensity,
      match_duration: matchData.rules?.customDuration,
      match_date: matchData.matchDate || new Date().toISOString()
    }]);
  
  return { data, error };
};
```

### Enhanced Match Queries
```javascript
// Get matches with rule information
const getEnhancedMatches = async (userId) => {
  const { data, error } = await supabase
    .from('enhanced_matches') // Using the enhanced view
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  
  return { data, error };
};

// Get matches by rule preset
const getMatchesByRulePreset = async (rulePreset) => {
  const { data, error } = await supabase
    .from('matches')
    .select('*, sports(name), locations(name)')
    .eq('rule_preset', rulePreset)
    .order('created_at', { ascending: false });
  
  return { data, error };
};

// Get matches by team size
const getMatchesByTeamSize = async (teamSize) => {
  const { data, error } = await supabase
    .from('matches')
    .select('*, sports(name)')
    .eq('team_size', teamSize)
    .order('created_at', { ascending: false });
  
  return { data, error };
};
```

### Location Services
```javascript
// Get locations by sport
const getLocationsBySport = async (sportName) => {
  const { data, error } = await supabase
    .from('locations')
    .select('*')
    .contains('sport_types', [sportName.toLowerCase()])
    .order('name');
  
  return { data, error };
};

// Search locations
const searchLocations = async (searchTerm) => {
  const { data, error } = await supabase
    .from('locations')
    .select('*')
    .or(`name.ilike.%${searchTerm}%,city.ilike.%${searchTerm}%,address.ilike.%${searchTerm}%`)
    .order('name');
  
  return { data, error };
};

// Add new location
const addLocation = async (locationData) => {
  const { data, error } = await supabase
    .from('locations')
    .insert([locationData]);
  
  return { data, error };
};
```

## 🎯 Rule Configuration API

### Get Sport Defaults
```javascript
const getSportDefaults = (sportName, teamSize, competitiveMode) => {
  // Implementation returns appropriate defaults based on:
  // - Sport type (basketball, football, etc.)
  // - Team size (1v1, 3v3, 5v5, etc.)
  // - Competitive mode (casual vs competitive)
  
  return {
    matchDuration: getDefaultDuration(sportName, teamSize),
    scoringSystem: getDefaultScoring(sportName, teamSize),
    rulePreset: competitiveMode ? getOfficialPreset(sportName) : 'casual',
    // ... other sport-specific defaults
  };
};
```

### Validate Rule Configuration
```javascript
const validateRules = (rules, sport, teamSize) => {
  const errors = [];
  
  // Validate rule consistency
  if (rules.shotClockEnabled && !rules.shotClockDuration) {
    errors.push('Shot clock duration required when shot clock is enabled');
  }
  
  // Validate team size compatibility
  if (teamSize === '1v1' && rules.teamFoulLimit > 0) {
    errors.push('Team fouls not applicable for 1v1 matches');
  }
  
  // Validate sport-specific rules
  if (sport === 'football' && rules.shotClockEnabled) {
    errors.push('Shot clock not applicable for football');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
```

## 📊 Statistics API

### Get Rule-Based Statistics
```javascript
// Get performance statistics based on rule configurations
const getRuleBasedStats = async (userId, rulePreset) => {
  const { data, error } = await supabase
    .from('enhanced_matches')
    .select('*')
    .eq('user_id', userId)
    .eq('rule_preset', rulePreset);
  
  // Calculate statistics specific to rule set
  const stats = calculateRuleSpecificStats(data);
  
  return { stats, error };
};

// Get team size performance
const getTeamSizePerformance = async (userId) => {
  const { data, error } = await supabase
    .from('matches')
    .select('team_size, team_a_score, team_b_score, /* other fields */')
    .eq('user_id', userId);
  
  // Group by team size and calculate performance metrics
  const performance = groupByTeamSize(data);
  
  return { performance, error };
};
```

## 🔍 Advanced Queries

### Complex Rule Filtering
```sql
-- Find matches with specific rule combinations
SELECT * FROM enhanced_matches 
WHERE (match_rules->>'shotClockEnabled')::boolean = true
  AND (match_rules->>'shotClockDuration')::integer <= 24
  AND team_size = '5v5'
  AND competitive_mode = true;

-- Get rule preset popularity
SELECT rule_preset, COUNT(*) as match_count
FROM matches 
WHERE rule_preset IS NOT NULL
GROUP BY rule_preset
ORDER BY match_count DESC;

-- Find location-based rule preferences
SELECT l.name, m.rule_preset, COUNT(*) as usage_count
FROM matches m
JOIN locations l ON m.location_id = l.id
WHERE m.rule_preset IS NOT NULL
GROUP BY l.name, m.rule_preset
ORDER BY l.name, usage_count DESC;
```

### Performance Analytics
```sql
-- Get match duration vs scoring statistics
SELECT 
  match_duration,
  AVG(team_a_score + team_b_score) as avg_total_score,
  COUNT(*) as match_count
FROM matches 
WHERE match_duration IS NOT NULL
GROUP BY match_duration
ORDER BY match_duration;

-- Analyze competitive vs casual performance
SELECT 
  competitive_mode,
  stat_tracking_intensity,
  AVG(team_a_score + team_b_score) as avg_score,
  COUNT(*) as matches
FROM matches
GROUP BY competitive_mode, stat_tracking_intensity;
```

## 🚀 Integration Examples

### React Native Component Integration
```javascript
// Enhanced match setup component
const EnhancedMatchSetup = () => {
  const [config, setConfig] = useState({
    sport: null,
    matchType: 'single',
    matchMode: 'past_entry',
    teamSize: '5v5',
    location: null,
    rules: {},
    competitiveMode: false
  });

  const handleRuleChange = (newRules) => {
    setConfig(prev => ({
      ...prev,
      rules: { ...prev.rules, ...newRules }
    }));
  };

  const createMatch = async () => {
    const matchData = await createEnhancedMatch(config);
    // Handle success/error
  };

  return (
    <MatchSetupFlow 
      config={config}
      onConfigChange={setConfig}
      onRuleChange={handleRuleChange}
      onSubmit={createMatch}
    />
  );
};
```

This comprehensive API documentation provides the foundation for implementing and extending Athlehub's enhanced match setup system with professional rule configurations and location-based features.
