# ⚖️ Athlehub Professional Rule Systems Documentation

## Overview
Athlehub implements comprehensive, professional-grade rule systems that match official sports organizations' standards. The system supports 15+ official rule presets with team size-specific adaptations and comprehensive customization options.

## 🏀 Basketball Rule Systems

### Official Rule Bodies Supported
- **FIBA** (International Basketball Federation)
- **NBA** (National Basketball Association)
- **NCAA** (National Collegiate Athletic Association)
- **FIBA 3x3** (Official 3-on-3 Basketball)

### Team Size Configurations

#### **1v1 Basketball**
```json
{
  "courtSize": "half_court",
  "winCondition": "first_to_points",
  "targetPoints": 11,
  "scoringSystem": "streetball", // 1s & 2s
  "shotClockEnabled": false,
  "personalFoulLimit": 0,
  "timeoutsPerTeam": 0
}
```

#### **2v2/3v3 Basketball (FIBA 3x3 Style)**
```json
{
  "rulePreset": "fiba_3x3",
  "courtSize": "half_court",
  "matchDuration": 10, // minutes
  "winCondition": "time_or_points",
  "targetPoints": 21,
  "scoringSystem": "fiba_3x3", // 1pt inside, 2pt outside
  "shotClockEnabled": true,
  "shotClockDuration": 12,
  "teamFoulLimit": 6, // penalty situation
  "personalFoulLimit": 0
}
```

#### **5v5 Basketball (Official)**
```json
{
  "rulePreset": "fiba", // or "nba", "ncaa"
  "courtSize": "full_court",
  "matchDuration": 40, // FIBA: 4×10, NBA: 4×12, NCAA: 2×20
  "quartersOrHalves": "quarters", // or "halves" for NCAA
  "scoringSystem": "standard", // 2s & 3s
  "shotClockEnabled": true,
  "shotClockDuration": 24, // NCAA: 30s
  "personalFoulLimit": 5, // NBA: 6
  "teamFoulLimit": 4, // bonus free throws
  "timeoutsPerTeam": 5 // varies by preset
}
```

### Basketball Rule Categories
- **Timing**: Game duration, shot clock, timeouts
- **Scoring**: 2-point, 3-point, free throw systems
- **Fouls**: Personal fouls, team fouls, bonus situations
- **Substitutions**: Dead ball substitutions, unlimited entries
- **Court**: Full court vs half court specifications

## ⚽ Football (Soccer) Rule Systems

### Official Rule Bodies Supported
- **FIFA 11v11** (Fédération Internationale de Football Association)
- **FIFA Futsal** (Indoor 5-a-side football)

### Team Size Configurations

#### **3v3/4v4 Football (Street/Pickup)**
```json
{
  "fieldSize": "mini",
  "matchDuration": 20, // 2×10 minutes
  "cardSystemEnabled": false,
  "offsideEnabled": false,
  "maxSubstitutions": "unlimited",
  "foulLimit": 0,
  "penaltyShootoutEnabled": false
}
```

#### **5v5 Football (Futsal)**
```json
{
  "rulePreset": "fifa_futsal",
  "fieldSize": "futsal",
  "matchDuration": 40, // 2×20 minutes
  "cardSystemEnabled": true,
  "offsideEnabled": false,
  "substitutionType": "rolling", // on-the-fly
  "maxSubstitutions": "unlimited",
  "foulLimit": 5, // per half, leads to penalty kicks
  "penaltyShootoutEnabled": true
}
```

#### **11v11 Football (FIFA)**
```json
{
  "rulePreset": "fifa",
  "fieldSize": "full",
  "matchDuration": 90, // 2×45 minutes
  "cardSystemEnabled": true,
  "offsideEnabled": true,
  "substitutionType": "windows", // 3 substitution windows
  "maxSubstitutions": "5",
  "extraTimeEnabled": false, // optional
  "penaltyShootoutEnabled": false // optional
}
```

### Football Rule Categories
- **Field**: Size specifications (mini, futsal, small, full)
- **Timing**: Match duration, extra time, injury time
- **Fouls**: Card system, foul accumulation, penalty kicks
- **Substitutions**: Rolling vs window-based, substitution limits
- **Offside**: Enforcement based on team size and format

## 🏐 Volleyball Rule Systems

### Official Rule Bodies Supported
- **FIVB Indoor** (Fédération Internationale de Volleyball)
- **FIVB Beach** (Beach volleyball regulations)

### Team Size Configurations

#### **2v2 Volleyball (Beach)**
```json
{
  "rulePreset": "fivb_beach",
  "courtType": "beach",
  "setsConfiguration": "best_of_3",
  "pointsPerSet": 21,
  "finalSetPoints": 15,
  "scoringSystem": "rally",
  "winByTwo": true,
  "maxSubstitutions": "0",
  "faultEnforcement": "strict",
  "rotationRules": false
}
```

#### **6v6 Volleyball (Official Indoor)**
```json
{
  "rulePreset": "fivb_indoor",
  "courtType": "indoor",
  "setsConfiguration": "best_of_5",
  "pointsPerSet": 25,
  "finalSetPoints": 15,
  "scoringSystem": "rally",
  "winByTwo": true,
  "maxSubstitutions": "6",
  "substitutionEntries": 12, // total entries per set
  "faultEnforcement": "strict",
  "rotationRules": true,
  "timeoutsPerTeam": 2
}
```

### Volleyball Rule Categories
- **Sets**: Best of 3/5, points per set, final set rules
- **Scoring**: Rally scoring vs side-out scoring
- **Rotation**: Player position rules and rotation order
- **Substitutions**: Limited entries and substitution tracking
- **Faults**: Net violations, rotation faults, service faults

## 🏸 Badminton Rule Systems

### Official Rule Bodies Supported
- **BWF** (Badminton World Federation)

### Configuration Examples

#### **Singles (1v1)**
```json
{
  "rulePreset": "bwf",
  "setsConfiguration": "best_of_3",
  "pointsPerSet": 21,
  "scoringSystem": "rally",
  "winByTwo": true,
  "serviceRotation": "every_2_points",
  "faultEnforcement": true,
  "maxSubstitutions": "0"
}
```

#### **Doubles (2v2)**
```json
{
  "rulePreset": "bwf",
  "setsConfiguration": "best_of_3",
  "pointsPerSet": 21,
  "scoringSystem": "rally",
  "winByTwo": true,
  "serviceRotation": "every_2_points",
  "doublesServiceOrder": true,
  "doublesCourtLines": true,
  "faultEnforcement": true
}
```

### Badminton Rule Categories
- **Scoring**: Rally scoring, win-by-2 rules
- **Service**: Rotation rules, doubles service order
- **Court**: Singles vs doubles court boundaries
- **Faults**: Net touches, service faults, court violations

## 🏓 Table Tennis Rule Systems

### Official Rule Bodies Supported
- **ITTF** (International Table Tennis Federation)

### Configuration Examples

#### **Singles (1v1)**
```json
{
  "rulePreset": "ittf",
  "setsConfiguration": "best_of_5", // or best_of_7
  "pointsPerSet": 11,
  "scoringSystem": "rally",
  "winByTwo": true,
  "serviceRotation": "every_2_serves",
  "faultEnforcement": true,
  "timeoutsPerTeam": 1
}
```

#### **Doubles (2v2)**
```json
{
  "rulePreset": "ittf",
  "setsConfiguration": "best_of_5",
  "pointsPerSet": 11,
  "scoringSystem": "rally",
  "winByTwo": true,
  "serviceRotation": "every_2_serves",
  "doublesServiceOrder": true, // strict alternating hits
  "faultEnforcement": true
}
```

### Table Tennis Rule Categories
- **Scoring**: Rally scoring, deuce rules (win by 2)
- **Service**: Rotation every 2 serves, doubles alternation
- **Equipment**: Paddle regulations, ball specifications
- **Faults**: Edge balls, net serves, illegal serves

## 🎯 Rule Customization System

### Custom Rule Override
Users can start with official presets and customize specific aspects:

```json
{
  "basePreset": "fiba",
  "customizations": {
    "matchDuration": 32, // Custom 4×8 minutes instead of 4×10
    "shotClockDuration": 20, // Custom shot clock
    "personalFoulLimit": 6, // NBA-style foul limit with FIBA timing
    "timeoutsPerTeam": 3 // Custom timeout allowance
  }
}
```

### Rule Validation System
- **Logical Consistency**: Ensures rule combinations make sense
- **Sport Compatibility**: Validates rules are appropriate for selected sport
- **Team Size Validation**: Confirms rules work with selected team size
- **Safety Checks**: Prevents dangerous or impossible configurations

### Rule Templates
- **Save Custom Rules**: Store frequently used custom configurations
- **Share Templates**: Export/import rule sets between users
- **Organization Presets**: Create league or organization-specific rules
- **Quick Selection**: Rapid access to saved rule combinations

## 🚀 Implementation Benefits

### **Professional Accuracy**
- Matches official rule bodies exactly
- Regular updates to reflect rule changes
- Comprehensive coverage of all major variations

### **Flexibility**
- Supports casual pickup to professional tournaments
- Customizable while maintaining rule integrity
- Easy switching between different rule sets

### **User Experience**
- Smart defaults reduce setup time
- Progressive disclosure shows only relevant options
- Clear rule descriptions and explanations

### **Data Quality**
- Consistent rule application enables accurate statistics
- Historical comparison across different rule sets
- Professional-level match documentation

### **Scalability**
- Easy to add new sports and rule variations
- Modular architecture supports complex rule interactions
- Ready for advanced features like live rule enforcement

## 📊 Statistics Integration

### Rule-Based Statistics
Different rule configurations enable different statistical tracking:

**Professional Rules (FIBA/NBA):**
- Advanced shooting statistics (FG%, 3P%, FT%)
- Plus/minus ratings
- Efficiency metrics
- Foul tracking with bonus situations

**Casual Rules:**
- Basic scoring statistics
- Simple win/loss records
- Participation tracking

**3x3 Rules:**
- Specialized 3x3 statistics
- Possession-based metrics
- Quick game analytics

### Performance Analytics
- **Rule Comparison**: Compare performance across different rule sets
- **Adaptation Metrics**: How players perform with different rules
- **Efficiency Analysis**: Optimal rule sets for different skill levels
- **Historical Trends**: Rule preference and performance over time
