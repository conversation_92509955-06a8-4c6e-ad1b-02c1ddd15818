# 🏆 Athlehub Match Formats Documentation

## Overview
Match formats define the structure and flow of a match in Athlehub. Each format influences scoring, progression, winner determination, and is adaptable across different sports and team sizes.

## 🔹 1. Single Game Format

### ✅ Description
A one-off game played between two teams where the winner is determined based on the final score at the end of the match duration or when a target score is reached.

### ⚙️ Characteristics
- **Straightforward Format**: Most intuitive and commonly used
- **Flexible Duration**: Time-bound, point-bound, or hybrid
- **Casual Friendly**: Perfect for pickup games and recreational play
- **Quick Setup**: Minimal configuration required

### 🛠 Configuration Options
| Setting | Options | Description |
|---------|---------|-------------|
| **Duration Type** | Time-bound, Point-bound, Hybrid | How the match ends |
| **Match Duration** | Custom minutes/quarters/halves | Total playing time |
| **Target Points** | Custom number | First to X points (point-bound) |
| **Tiebreaker** | Sudden death, Golden goal, Free throws | Overtime resolution |
| **Scoring System** | Sport-specific options | How points are awarded |

### 📌 Use Cases
- **5v5 Basketball Pickup**: 40 minutes, 4×10 quarters, standard scoring
- **3v3 Football Street**: First to 5 goals or 20 minutes
- **1v1 Table Tennis**: First to 11 points, win by 2
- **6v6 Volleyball**: Best of 3 sets to 25 points

### 🎯 Sport-Specific Adaptations
**Basketball:**
- Time-based: 4 quarters or 2 halves
- Point-based: First to 11, 15, or 21
- Hybrid: 10 minutes OR first to 21

**Football:**
- Time-based: 2×20, 2×30, or 2×45 minutes
- Point-based: First to 3, 5, or 7 goals
- Golden goal overtime available

**Volleyball:**
- Set-based within single game: Best of 3 or 5 sets
- Point target: 21 or 25 points per set
- Final set: Usually to 15 points

## 🔹 2. Set-Based Match Format

### ✅ Description
The match is divided into multiple sets or games, and the winner is the first team to win a majority of them. Natural for sports with inherent game segmentation.

### ⚙️ Characteristics
- **Multi-Game Structure**: Each set is a complete game
- **Majority Winner**: Best of 3, 5, or 7 format
- **Momentum Shifts**: Allows for comebacks between sets
- **Professional Standard**: Used in most official tournaments

### 🛠 Configuration Options
| Setting | Options | Description |
|---------|---------|-------------|
| **Set Count** | Best of 3, 5, 7 | Number of sets to play |
| **Points per Set** | 11, 15, 21, 25 | Target points for each set |
| **Final Set Rules** | Same points, Reduced points | Special rules for deciding set |
| **Win by 2** | Enabled/Disabled | Must win by 2 points |
| **Side Switch** | Each set, Every 2 sets | When teams switch sides |

### 📌 Use Cases
- **Badminton Singles**: Best of 3 to 21 points, rally scoring
- **Table Tennis Match**: Best of 5 to 11 points, serve every 2 points
- **Volleyball Tournament**: Best of 5 to 25 points, final set to 15
- **Tennis-Style Format**: Best of 3 or 5 sets with games and points

### 🎯 Sport-Specific Rules
**Badminton:**
- Best of 3 sets to 21 points
- Rally scoring (point on every rally)
- Win by 2, maximum 30 points
- Service alternates every 2 points

**Table Tennis:**
- Best of 5 or 7 sets to 11 points
- Service alternates every 2 serves
- Win by 2, no maximum limit
- Doubles: Alternating hits within team

**Volleyball:**
- Best of 5 sets: First 4 to 25, final to 15
- Rally scoring system
- Win by 2 points required
- Team rotation after each set

## 🔹 3. Tournament Format

### ✅ Description
A structured series of matches where teams compete in elimination or round-robin format to crown an overall winner. Matches can be Single Game or Set-Based within the tournament structure.

### ⚙️ Characteristics
- **Multi-Match Structure**: Series of connected matches
- **Bracket Management**: Elimination or group stage progression
- **Scalable**: Supports 4 to 64+ teams
- **Professional Organization**: Full tournament administration

### 🛠 Configuration Options
| Setting | Options | Description |
|---------|---------|-------------|
| **Bracket Type** | Single Elimination, Double Elimination, Round Robin, Swiss | Tournament structure |
| **Match Format** | Single Game, Set-Based | Format for each match |
| **Seeding** | Random, Manual, Ranked | How teams are placed |
| **Advancement** | Win/Loss, Points, Goal Difference | How teams advance |
| **Finals Format** | Same as matches, Extended format | Special rules for finals |

### 📌 Use Cases
- **Weekend Basketball Tournament**: 8 teams, single elimination, 5v5 games
- **Football League**: 12 teams, round-robin, 11v11 matches
- **Table Tennis Championship**: 16 players, double elimination, best of 5 sets
- **Volleyball Tournament**: 6 teams, group stage + knockout, best of 3 sets

### 🎯 Tournament Types
**Single Elimination:**
- Lose once, you're out
- Fast progression, fewer total matches
- High stakes for each match

**Double Elimination:**
- Two losses required for elimination
- Winners and losers brackets
- More matches, fairer for close competition

**Round Robin:**
- Everyone plays everyone
- Most matches, most comprehensive
- Best for leagues and skill assessment

**Swiss System:**
- Pair teams with similar records
- Predetermined number of rounds
- Good for large tournaments

## 🔹 4. Rotational Play (Winner Stays) Format

### ✅ Description
Multiple teams rotate into the game in a "Winner Stays On" or "King of the Court" fashion. Teams play short games to a winning condition, with winners continuing against new challengers.

### ⚙️ Characteristics
- **Continuous Play**: Minimal downtime between games
- **Multiple Teams**: 3+ teams in rotation
- **Short Games**: Quick matches to maintain flow
- **Social Format**: Great for courts with many players

### 🛠 Configuration Options
| Setting | Options | Description |
|---------|---------|-------------|
| **Team Count** | 3-8 teams | Number of teams in rotation |
| **Game Duration** | Time limit, Point target | How each game ends |
| **Queue System** | Fixed order, Random, Challenge | How next team is selected |
| **Win Limit** | Unlimited, Max consecutive wins | Prevent team dominance |
| **Tiebreaker Rules** | Multiple options | How to resolve draws |

### 📌 Use Cases
- **3v3 Basketball Court**: 5 teams, first to 11 points, winner stays
- **4v4 Football Pitch**: 6 teams, 10-minute games, random rotation
- **2v2 Beach Volleyball**: 4 teams, first to 15 points, challenge system
- **King of the Court**: Winner stays until beaten or max 3 wins

### 🎯 Tiebreaker Systems
When games end in draws, several resolution methods are available:

#### **1. Sudden Death (With Timer)**
- Short extra time period (1-2 minutes)
- First team to score wins
- Fallback to other methods if still tied

#### **2. Sudden Death (No Timer)**
- Game continues until one team scores
- No time limit, pure next-point-wins
- Can cause delays with evenly matched teams

#### **3. Both Teams Rotate Out**
- Neither team wins the draw
- Both go to bottom of rotation queue
- Next two teams start fresh game

#### **4. Coin Toss / Random Selection**
- App randomly selects winner
- Fast resolution for time-constrained courts
- Optional manual override by referee

#### **5. Team with Higher Activity**
- Team with more shots, possession, or fewer fouls wins
- Requires active stat tracking (Intermediate+ level)
- Rewards aggressive, clean play

### 🛠️ Tiebreaker Selection Logic
| Sport | Team Size | Suggested Default | Reasoning |
|-------|-----------|------------------|-----------|
| Basketball | 3v3, 5v5 | Sudden Death (1-min timer) | Quick scoring likely |
| Football | 3v3-7v7 | Sudden Death (no timer) | Goals less frequent |
| Football | 11v11 | Both Rotate Out | Longer games, fairness |
| Volleyball | All | Both Rotate Out | Sets already determine winners |
| Table Tennis | 1v1, 2v2 | Sudden Death Point | Quick resolution |
| Badminton | All | Most Active Team | Rewards aggressive play |

## 🎯 Format Selection Guidelines

### **Casual Play**
- **Single Game**: Quick pickup games, flexible rules
- **Rotational Play**: Multiple teams, social atmosphere

### **Recreational Leagues**
- **Single Game**: Regular season matches
- **Set-Based**: Sports with natural set structure
- **Tournament**: End-of-season playoffs

### **Competitive/Professional**
- **Set-Based**: Official tournament standards
- **Tournament**: Organized competitions with brackets
- **Single Game**: Time-constrained professional matches

### **Training/Practice**
- **Rotational Play**: Skill development with multiple opponents
- **Single Game**: Focused practice matches
- **Set-Based**: Endurance and consistency training

## 🚀 Implementation Benefits

### **Flexibility**
- Supports all play styles from casual to professional
- Adaptable across different sports and team sizes
- Configurable rules for specific needs

### **User Experience**
- Clear format selection with guided setup
- Appropriate defaults for each sport and context
- Comprehensive validation and error handling

### **Data Quality**
- Consistent match structure enables better analytics
- Format-specific statistics tracking
- Historical data comparison across similar formats

### **Scalability**
- Easy to add new formats and variations
- Modular architecture supports complex tournament structures
- Ready for advanced features like live streaming and spectator modes
