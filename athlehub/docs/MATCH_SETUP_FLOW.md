# 🧭 Athlehub – Enhanced Match Setup Flow Documentation

## Overview
Athlehub's enhanced match setup flow provides a comprehensive, 6-step guided process for creating professional-grade sports matches with detailed rule configurations, team management, and statistics tracking.

## 🔹 Step 1: Select Sport + Match Type

### 1.1 Select Sport
Available sports with comprehensive rule support:
- **🏀 Basketball** (1v1, 2v2, 3v3, 4v4, 5v5)
- **⚽ Football/Soccer** (3v3, 4v4, 5v5, 7v7, 11v11)
- **🏐 Volleyball** (2v2, 3v3, 4v4, 6v6)
- **🏓 Table Tennis** (1v1, 2v2)
- **🏸 Badminton** (1v1, 2v2)

**Sport Selection Impact:**
- Defines allowed match modes and team sizes
- Determines available rule presets (NBA, FIBA, FIFA, etc.)
- Sets relevant stat categories and scoring systems
- Configures sport-specific validation rules

### 1.2 Select Match Type
| Match Type | Description | Sports Supported |
|------------|-------------|------------------|
| **Live Match** | In-app match clock, real-time scoring, live stat tracking | All |
| **Log Past Match** | Manual stat entry for completed game, no timer | All |

**Match Type Impact:**
- Sets overall flow behavior (editable date, real-time UI)
- Determines stat entry mode (live vs retrospective)
- Configures timer and clock functionality

## 🔹 Step 2: Match Mode + Team Size + Location + Date

### 2.1 Match Mode Selection
| Mode | Description | Sports Supported | Key Features |
|------|-------------|------------------|--------------|
| **Single Game** | Standard match with final score | All | Most common format, straightforward scoring |
| **Set-Based Match** | Multiple sets (best of 3/5) | Volleyball, Table Tennis, Badminton | Set tracking, win-by-2 rules |
| **Tournament** | Part of bracket/league | All | Bracket tracking, match numbering |
| **Rotational Play** | Winner stays, multiple teams | Basketball, Football, Volleyball | Queue management, tiebreakers |

**Mode-Specific Features:**
- **Single Game**: Time-bound or point-bound options
- **Set-Based**: Configurable set count and points per set
- **Tournament**: Bracket type selection and round tracking
- **Rotational**: Team rotation and tiebreaker rules

### 2.2 Team Size Selection
**Dynamic Options Based on Sport:**
- **Basketball**: 1v1, 2v2, 3v3, 4v4, 5v5
- **Football**: 3v3, 4v4, 5v5, 7v7, 11v11
- **Volleyball**: 2v2, 3v3, 4v4, 6v6
- **Racket Sports**: 1v1, 2v2

**Team Size Impact:**
- Determines minimum player count validation
- Selects appropriate rule presets (e.g., FIBA 3x3 for 3v3 basketball)
- Configures court/field size requirements
- Sets substitution and rotation rules

### 2.3 Location Selection
**Features:**
- **Searchable Database**: Auto-suggest from 15+ predefined courts
- **Add New Location**: Manual form for custom courts
- **Smart Filtering**: Show only relevant courts for selected sport

**Location Benefits:**
- **Player Suggestions**: Auto-suggest regular players from location history
- **Team Recommendations**: Frequent team combinations at location
- **Leaderboards**: Location-based performance tracking
- **Court Analytics**: Popular courts and usage patterns

### 2.4 Date Configuration
- **Live Match**: Auto-set to current date/time
- **Past Match**: Custom date picker with validation
- **Future Match**: Schedule upcoming matches (tournament mode)

## 🔹 Step 3: Team Setup + Players

### 3.1 Team Name Configuration
- **Optional**: Single and Set-Based matches
- **Required**: Tournament mode for bracket display
- **Smart Defaults**: Location-based team name suggestions

### 3.2 Player Management
**Player Addition Methods:**
- **Manual Input**: Direct name entry with validation
- **Auto-Suggestions**: Based on location history and previous matches
- **Saved Teams**: Quick selection from previously used team configurations

**Player Information:**
- **Name**: Required field with duplicate validation
- **Jersey Number**: Optional (required in Competitive Mode)
- **Position**: Sport-specific position tracking
- **Player History**: Automatic match count updates

**Competitive Mode Features:**
- **Jersey Number Enforcement**: Required and unique within team
- **Enhanced Validation**: Stricter player and team rules
- **Leaderboard Tracking**: Performance statistics for rankings
- **Advanced Statistics**: Professional-level stat tracking

## 🔹 Step 4: Configure Match Rules

### 4.1 Rule Preset Selection
**Official Rule Bodies:**
- **Basketball**: NBA, FIBA, NCAA, FIBA 3x3
- **Football**: FIFA 11v11, FIFA Futsal
- **Volleyball**: FIVB Indoor, FIVB Beach
- **Racket Sports**: BWF (Badminton), ITTF (Table Tennis)

### 4.2 Dynamic Rule Configuration
**Categories Shown Based on Sport & Team Size:**

#### **Timing Configuration**
- Match duration (quarters, halves, sets, continuous)
- Overtime and tiebreaker rules
- Shot clock settings (basketball)
- Time limits per set/game

#### **Scoring Systems**
- **Basketball**: 2s/3s, 1s/2s, FIBA 3x3 (1pt inside/2pt outside)
- **Football**: Goals, penalty systems
- **Volleyball**: Rally scoring, side-out scoring
- **Racket Sports**: Rally scoring, traditional scoring

#### **Foul & Penalty Systems**
- Personal foul limits (basketball)
- Team foul limits with bonus situations
- Card systems (football - yellow/red cards)
- Fault enforcement levels (strict, relaxed, none)

#### **Substitution Rules**
- Unlimited vs limited substitutions
- Rolling subs vs window-based subs (football)
- Substitution entry tracking (volleyball)
- Dead ball vs live ball substitutions

#### **Court/Field Specifications**
- Court size (half-court, full-court)
- Field size (mini, futsal, small, full pitch)
- Surface type (indoor, beach, grass)
- Boundary enforcement rules

#### **Advanced Sport-Specific Rules**
- **Basketball**: Shot clock, three-point line, bonus situations
- **Football**: Offside rules, card accumulation, penalty shootouts
- **Volleyball**: Rotation rules, net fault enforcement, service order
- **Racket Sports**: Service rotation, doubles court lines, win-by-2 rules

### 4.3 Custom Rule Override
- **Preset Modification**: Start with official rules and customize
- **Full Custom**: Create completely custom rule sets
- **Rule Templates**: Save and reuse custom configurations
- **Validation**: Ensure rule combinations are logical and valid

## 🔹 Step 5: Select Stat Tracking Intensity

### 5.1 Intensity Levels
| Level | Description | Tracked Statistics | Use Cases |
|-------|-------------|-------------------|-----------|
| **Basic** | Points/goals only | Score, basic outcomes | Casual pickup games |
| **Intermediate** | Core stats | Assists, rebounds, saves, cards | Recreational leagues |
| **Advanced** | Detailed metrics | Shooting %, efficiency ratios, advanced stats | Competitive leagues |
| **Professional** | AI-generated insights | Possession %, xG, heat maps, performance analytics | Professional/semi-pro |

### 5.2 Sport-Specific Statistics
**Basketball:**
- **Basic**: Points scored
- **Intermediate**: Assists, rebounds, steals, blocks
- **Advanced**: Field goal %, free throw %, plus/minus
- **Professional**: Effective field goal %, true shooting %, usage rate

**Football:**
- **Basic**: Goals scored
- **Intermediate**: Assists, saves, cards, shots
- **Advanced**: Pass completion %, tackles, interceptions
- **Professional**: Expected goals (xG), heat maps, possession %

**Volleyball:**
- **Basic**: Points scored
- **Intermediate**: Spikes, digs, serve aces, blocks
- **Advanced**: Attack %, serve %, reception quality
- **Professional**: Efficiency ratings, court coverage, set quality

## 🔹 Step 6: Summary → Validation → Start/Submit

### 6.1 Match Summary Display
**Comprehensive Review:**
- Sport and match type configuration
- Team setup and player roster
- Match rules and format details
- Statistics tracking level
- Location and date information

### 6.2 Validation Checks
**Required Field Validation:**
- All mandatory fields completed
- Team/player count matches selected team size
- Valid rule combinations for format
- Location and date validation

**Logic Validation:**
- Rule consistency (e.g., no 11v11 in FIBA 3x3)
- Player count vs team size alignment
- Jersey number uniqueness (competitive mode)
- Date/time logical constraints

### 6.3 Action Buttons
**Live Match:**
- **"Start Live Match"** → Navigate to real-time tracker
- Initializes match timer and live scoring interface
- Enables real-time stat tracking based on intensity level

**Past Match:**
- **"Submit Match Log"** → Save to match history
- Creates completed match record with final statistics
- Updates player and team performance metrics

## 🎯 Implementation Benefits

### **User Experience**
- **Guided Process**: Step-by-step flow reduces complexity
- **Smart Defaults**: Intelligent suggestions based on selections
- **Progressive Disclosure**: Show only relevant options
- **Validation Feedback**: Clear error messages and guidance

### **Data Quality**
- **Comprehensive Configuration**: Detailed match setup ensures accurate data
- **Rule Enforcement**: Professional-level rule validation
- **Consistent Statistics**: Standardized stat tracking across matches
- **Location Intelligence**: Enhanced data through location-based features

### **Scalability**
- **Modular Architecture**: Easy to add new sports and rules
- **Flexible Configuration**: Supports casual to professional use cases
- **Database Optimization**: Efficient storage and querying of complex rule data
- **Future-Proof Design**: Ready for advanced features like tournaments and analytics
